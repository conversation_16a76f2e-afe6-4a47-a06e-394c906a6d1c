import * as vscode from "vscode"
import { v4 as uuidv4 } from "uuid"

import { StatusBar } from "./statusbar"
import { generateCtrlArrowDownDecorationType } from "./decorations"
import {
	IGNORE_TRIGGER_CHAR,
	IGNORE_TRIGGER_SAME_CHAR,
	isNumericRegex,
	SPECIAL_TRIGGER_AFTER_CHAR,
	TRIGGER_CHAR,
} from "./rule"
import { AESDecrypt, AESEncrypt, buildOpUrl, getOsPlatformKeys } from "../utils/eop"
import { ContextProxy } from "../core/config/ContextProxy"
import { importDefinitionsService, importFunsTimeout, rootPathContextService } from "../cross-file/symbol.util"
import { DomSymbol } from "../cross-file/codelens.util"
import {
	SUGGESTION_EVENT,
	SUGGESTION_EVENT_MESSAGE,
	SUGGESTION_EVENT_REASON,
	toTriggerSuggestionTriggerEvent,
} from "./suggestionEvent"
import { ActiveEditorTracker } from "../core/ActiveEditorTracker"

//实现代码提示
export class AiProvider implements vscode.InlineCompletionItemProvider {
	private _debouncer: NodeJS.Timeout | any // 代码补全
	private _context: vscode.InlineCompletionContext | undefined = undefined // 上一次代码补全对应的context

	osPlatformKeys = getOsPlatformKeys()
	private ctrlBracketContentText = `快捷键 ${this.osPlatformKeys.alt} + [ 和 ${this.osPlatformKeys.alt} + ] 进行补全选择`
	private ctrlArrowDownContentText = `${this.osPlatformKeys.ctrl} + ↓ 逐行采纳补全; Tab接受补全`
	private ctrlArrowBracketDownContentText = `快捷键 ${this.osPlatformKeys.alt} + [ 和 ${this.osPlatformKeys.alt} + ] 进行补全选择；${this.osPlatformKeys.ctrl} + ↓ 逐行采纳补全`
	private ctrlTabBracketDownContentText = `快捷键 ${this.osPlatformKeys.alt} + [ 和 ${this.osPlatformKeys.alt} + ] 进行补全选择；Tab接受补全`
	private ctrlTabEnterContentText = "Tab键接受补全"
	private ctrlKContentText = `${this.osPlatformKeys.ctrl} + K 手动触发代码补全`

	lastSelectedCompletionInfo: vscode.SelectedCompletionInfo | undefined = undefined // 最新的SelectedCompletionInfo，有没有触发代码补全不确定

	inlineCompletionItems: vscode.InlineCompletionItem[] | undefined = undefined // 最新代码补全
	inlineCompletionItemsBackup: vscode.InlineCompletionItem[] = [] // 最新代码补全备份
	inlineCompletionItemsBackupShow: vscode.InlineCompletionItem[] = [] // 展示的"新代码补",是由inlineCompletionItemsBackup加工而来
	inlineCompletionItemsBackupShowAutomatic: vscode.InlineCompletionItem[] | undefined = undefined // 展示的"新代码补",是由inlineCompletionItemsBackupShow加工而来, Automatic = 1触发形成, 供Invoke = 0判定之前Automatic = 1是否触发

	theRequestIdBackup: any = undefined // 最新代码补全 request id 备份

	secondTextDocumentChangeEvent: any = undefined // 文档倒数第二次修改
	lastTextDocumentChangeEvent: any = undefined // 文档最后一次修改，用于判定是不是删除
	isCtrlKTrigger = false // 接下来的代码补全触发，是手动快捷键ctrl k触发
	isImmediateTrigger = false /// 立即触发，比如换行或ctrl k这种带着使用者意愿的触发

	isEnter: boolean = false // 上一次键盘按键是否是enter
	isAccept: boolean = false // 是否接受了InlineCompletion, 规避接收建议引起的二次触发
	isLineAccept: boolean = false // 上一次接受补全是逐行补全
	lineAcceptText = "" // 上一次逐行接受后剩余的补全代码
	ctrlArrowDownDecorationType: vscode.TextEditorDecorationType | undefined
	ctrlKDecorationType: vscode.TextEditorDecorationType | undefined

	// 延迟初始化的组件
	private _contextProxy: ContextProxy | undefined
	private _statusBar: StatusBar | undefined
	private _isInitialized = false
	private _initializationPromise: Promise<void> | undefined
	clipping = ""
	private activeEditorTracker: ActiveEditorTracker = ActiveEditorTracker.getInstance()

	constructor(
		public context: vscode.ExtensionContext,
		public outputChannel: vscode.OutputChannel,
		private domSymbol: DomSymbol,
	) {
		// 立即注册命令和事件监听器，这些是必须同步完成的
		this.toRegisterCommand()
		this._registerEventListeners()

		// 其他重型操作留给异步初始化
	}

	// 懒加载 ContextProxy
	get contextProxy(): ContextProxy {
		if (!this._contextProxy) {
			this._contextProxy = new ContextProxy(this.context)
		}
		return this._contextProxy
	}

	// 懒加载 StatusBar
	public get statusBar(): StatusBar {
		if (!this._statusBar) {
			this._statusBar = new StatusBar(this.context)
		}
		return this._statusBar
	}

	// 异步初始化方法
	public async initialize(): Promise<void> {
		if (this._isInitialized) {
			return
		}

		if (this._initializationPromise) {
			return this._initializationPromise
		}

		this._initializationPromise = this._performInitialization()
		return this._initializationPromise
	}

	// 预加载方法 - 后台异步初始化，提升首次使用体验
	public preload(): void {
		if (!this._isInitialized && !this._initializationPromise) {
			// 在后台启动初始化，但不等待完成
			this.initialize().catch((error) => {
				console.error("AiProvider preload failed:", error)
			})
		}
	}

	// 执行实际的初始化工作
	private async _performInitialization(): Promise<void> {
		try {
			// 并行初始化异步组件（命令和事件监听器已在构造函数中同步完成）
			await Promise.all([this._initializeContextProxy(), this._initializeStatusBar()])

			this._isInitialized = true
		} catch (error) {
			console.error("AiProvider initialization failed:", error)
			throw error
		}
	}

	// 初始化 ContextProxy
	private async _initializeContextProxy(): Promise<void> {
		// 通过 getter 触发懒加载
		const proxy = this.contextProxy
		if (!proxy.isInitialized) {
			await proxy.initialize()
		}
	}

	// 初始化 StatusBar
	private async _initializeStatusBar(): Promise<void> {
		// 通过 getter 触发懒加载，StatusBar 初始化通常是同步的
		await Promise.resolve(this.statusBar)
	}

	// 注册事件监听器 - 改为同步方法，在构造函数中立即调用
	private _registerEventListeners(): void {
		this.context.subscriptions.push(
			vscode.window.onDidChangeActiveTextEditor((_textEditor: vscode.TextEditor | undefined) => {
				this.secondTextDocumentChangeEvent = undefined
				this.lastTextDocumentChangeEvent = undefined
			}),
		)
		// 文档内容变更，enter键监听
		this.context.subscriptions.push(
			vscode.workspace.onDidChangeTextDocument((event: vscode.TextDocumentChangeEvent) => {
				// console.log(444400, event)
				if (!event.contentChanges[0]) {
					return
				}
				this.secondTextDocumentChangeEvent = this.lastTextDocumentChangeEvent
				this.lastTextDocumentChangeEvent = event
				// console.log(444411, event, this.secondTextDocumentChangeEvent)
				this.isEnter =
					this.lastTextDocumentChangeEvent.contentChanges.some(
						(item: vscode.TextDocumentContentChangeEvent) =>
							(item.text.startsWith("\n") || item.text.startsWith("\r\n")) && !item.text.trim().length,
					) ||
					(this.secondTextDocumentChangeEvent?.contentChanges[0] &&
						this.secondTextDocumentChangeEvent.contentChanges.some(
							(item: vscode.TextDocumentContentChangeEvent) =>
								(item.text.startsWith("\n") || item.text.startsWith("\r\n")) &&
								!item.text.trim().length,
						))
				if (this.ctrlArrowDownDecorationType) {
					this.ctrlArrowDownDecorationType.dispose()
					this.ctrlArrowDownDecorationType = undefined
				}
				if (this.ctrlKDecorationType) {
					this.ctrlKDecorationType.dispose()
					this.ctrlKDecorationType = undefined
				}
				// console.log(555444, this.isEnter);
			}),
		)
	}

	// 注册指令
	toRegisterCommand() {
		// Ctrl+K手动触发代码补全
		this.context.subscriptions.push(
			vscode.commands.registerCommand("zhanlu.inlineSugges.trigger", () => {
				this.isCtrlKTrigger = true

				vscode.commands.executeCommand("editor.action.inlineSuggest.trigger")
				if (this.ctrlKDecorationType) {
					this.ctrlKDecorationType.dispose()
				}
			}),
		)

		// 采纳 或 显示 代码提示
		// request对应代码补全请求request id
		this.context.subscriptions.push(
			vscode.commands.registerCommand(
				"zhanlu.accept_code",
				(insertText: string, rquest: string, event_name: string, clipping = this.clipping) => {
					if (event_name === "suggestion_accepted") {
						this.isAccept = true
						this.inlineCompletionItems = undefined
						this.theRequestIdBackup = undefined
					}

					const als = this.contextProxy.getAsl() as { [key: string]: string }
					const urlPath: string = "/api/acepilot/zhanlu/event"
					const requestUrl = buildOpUrl(urlPath, als, this.contextProxy.getBaseUrl())
					const { token } = als
					const theBody = {
						data: AESEncrypt(
							JSON.stringify({
								event_name,
								service_type: "completions",
								completion: insertText,
								plugin_version: this.context.extension?.packageJSON?.version ?? "",
								plugin_type: "vscode",
								event_id: rquest,
								languageType: vscode.window.activeTextEditor?.document.languageId,
								clipping,
							}),
							token,
						),
					}
					const requestId = uuidv4()
					// console.log(4466, requestId, insertText, rquest, event_name, clipping)
					fetch(requestUrl, {
						method: "post",
						headers: {
							"Content-Type": "application/json",
							request: requestId,
						},
						body: JSON.stringify(theBody),
					})
						.then((response) => response.json())
						.then((_res: any) => {})
				},
			),
		)

		// 单行接受代码补全
		this.context.subscriptions.push(
			vscode.commands.registerCommand("zhanlu.accept_inlineSuggestion_line", () => {
				const theInlineCompletionItems =
					this.inlineCompletionItemsBackupShow![0] ?? this.inlineCompletionItemsBackup![0]
				const theInsertText = theInlineCompletionItems?.insertText as string
				const thePosition = theInlineCompletionItems?.range?.start
				// console.log(555, theInsertText, thePosition)
				const lines = theInsertText.split("\n")
				// console.log(555111, lines)
				const editor = vscode.window.activeTextEditor
				if (lines?.length) {
					editor?.edit((edit: vscode.TextEditorEdit) => {
						if (lines.length === 1) {
							this.isAccept = true
							this.lineAcceptText = ""
							edit.insert(thePosition as vscode.Position, lines[0])
							return
						}

						if (lines?.length) {
							const theLastAry = lines.slice(1)
							this.isLineAccept = true
							// 获取下一行代码的空白位置，和本行一起插入, 从而保证光标位置
							const nextLineStartSpaces = lines[1].length - lines[1].trimStart().length
							const nextLineSpace = theLastAry[0].slice(0, nextLineStartSpaces)

							theLastAry[0] = theLastAry[0].trimStart()
							this.lineAcceptText = theLastAry.join("\n")

							edit.insert(thePosition as vscode.Position, `${lines[0]}\n${nextLineSpace}`)

							if (this.theRequestIdBackup) {
								vscode.commands.executeCommand(
									"zhanlu.accept_code",
									this.inlineCompletionItemsBackup![0].insertText,
									this.theRequestIdBackup,
									"suggestion_accepted",
								)
							}
						}
					})
				}
			}),
		)
	}

	private async buildRequestHeadersBody(
		document: vscode.TextDocument,
		position: vscode.Position,
		quickSuggestionPrefix: string,
	): Promise<any> {
		const line = document.lineAt(position.line)

		const line_type = line.text.trim()?.length ? "SINGLE_LINE" : "MULTI_LINE"
		const prompt = document.getText(new vscode.Range(new vscode.Position(0, 0), position))
		const suffix = document.getText().slice(prompt.length)
		// 从湛卢的GlobalState中获取补全配置
		const contextProxy = this.contextProxy
		const max_tokens_completion = contextProxy.getValue("maxTokensCompletion") ?? 64
		const completion_number = contextProxy.getValue("completionNumber") ?? 1
		const inlineCompletion_granularity = contextProxy.getValue("inlineCompletionGranularity") ?? "均衡"

		const requestBody: any = {
			prompt: prompt + quickSuggestionPrefix,
			suffix,
			max_completion_tokens: max_tokens_completion,
			inputs: {
				line_type,
				file_name: document?.fileName,
				limit_lines_factor: 2,
			},
			n: completion_number,
		}
		if (line_type === "MULTI_LINE") {
			if (inlineCompletion_granularity === "单行") {
				requestBody.inputs["line_type"] = "SINGLE_LINE"
			} else if (inlineCompletion_granularity === "一次性最大化") {
				requestBody.inputs["limit_lines_factor"] = 0
			} else if (inlineCompletion_granularity === "均衡") {
				requestBody.inputs["limit_lines_factor"] = 2
			}
		}
		// console.log(44440,inlineCompletion_granularity, requestBody);
		const requestHeaders = {}

		return {
			requestBody,
			requestHeaders,
		}
	}

	// event_name: 'suggestion_not_trigger' 'suggestion_trigger'
	// 代码改动触不触发补全的事件反馈
	async triggerSuggestionTriggerEvent(
		event_name: SUGGESTION_EVENT,
		document: vscode.TextDocument,
		position: vscode.Position,
		reason: SUGGESTION_EVENT_REASON,
		event_id: string,
	) {
		const prompt = document.getText(new vscode.Range(new vscode.Position(0, 0), position))
		const suffix = document.getText().slice(prompt.length)
		const body = {
			event_name,
			plugin_version: this.context.extension?.packageJSON?.version ?? "",
			languageType: document.languageId,
			file_name: document.fileName,
			clipping: `${SUGGESTION_EVENT_MESSAGE[reason]}, ${event_name === "suggestion_trigger" ? "触发补全" : "不触发补全"}`,
			prompt,
			suffix,
			event_id,
		}

		const params = {
			als: this.contextProxy.getAsl(),
			baseUrl: this.contextProxy.getBaseUrl(),
		}
		toTriggerSuggestionTriggerEvent(body, params)
	}

	// 不触发代码补全, true不触发, false触发
	private shouldSkipCompletion(document: vscode.TextDocument, position: vscode.Position, request: string): boolean {
		const theContentChanges = this.lastTextDocumentChangeEvent.contentChanges[0]
		// console.log(
		// 	4444222,
		// 	this.isEnter,
		// 	this.lastTextDocumentChangeEvent,
		// 	theContentChanges,
		// 	theContentChanges?.text,
		// 	theContentChanges?.text === "",
		// )
		// 删除不触发补全
		if (!this.isEnter && theContentChanges?.text === "" && theContentChanges?.rangeLength) {
			this.triggerSuggestionTriggerEvent(
				"suggestion_not_trigger",
				document,
				position,
				"delete_not_trigger",
				request,
			)
			return true
		}

		// 新增加特殊字符, 不触发补全
		if (IGNORE_TRIGGER_CHAR.includes(theContentChanges?.text.trim())) {
			this.triggerSuggestionTriggerEvent(
				"suggestion_not_trigger",
				document,
				position,
				"add_special_char_not_trigger",
				request,
			)
			return true
		}

		// 前后两次输入相同，不触发
		if (
			theContentChanges?.text === this.secondTextDocumentChangeEvent?.contentChanges[0]?.text &&
			IGNORE_TRIGGER_SAME_CHAR.includes(theContentChanges?.text)
		) {
			this.triggerSuggestionTriggerEvent(
				"suggestion_not_trigger",
				document,
				position,
				"input_same_char_not_trigger",
				request,
			)
			return true
		}

		// 光标前是空字符串、数字，不触发补全
		const theLinetext = document.lineAt(position).text
		const cursorBeforeChar = theLinetext.slice(position.character - 1, position.character)
		const cursorAfterChar = theLinetext.slice(position.character, position.character + 1)
		//console.log(4444333, cursorBeforeChar, cursorAfterChar)
		if (isNumericRegex(cursorBeforeChar)) {
			this.triggerSuggestionTriggerEvent(
				"suggestion_not_trigger",
				document,
				position,
				"before_cursor_char_not_trigger",
				request,
			)
			return true
		}

		if (TRIGGER_CHAR.includes(theContentChanges?.text)) {
			// console.log(4444555, cursorBeforeChar, cursorAfterChar);
			this.triggerSuggestionTriggerEvent(
				"suggestion_trigger",
				document,
				position,
				"add_special_char_trigger",
				request,
			)
			this.clipping = `${SUGGESTION_EVENT_MESSAGE["add_special_char_trigger"]}, 触发补全`
			return false
		}

		//console.log(4444444, cursorBeforeChar === " ", cursorAfterChar.trim().length, this.isEnter)
		// enter换行代码补全逻辑
		if (this.isEnter) {
			this.isEnter = false
			// 从湛卢的GlobalState中获取multiple_line_Completion配置
			const multiple_line_Completion = this.contextProxy.getValue("multipleLineCompletion") ?? "自动补全"
			// console.log(2222, multiple_line_Completion, this.isEnter, this.isImmediateTrigger)
			if (multiple_line_Completion === "触发补全") {
				this.triggerSuggestionTriggerEvent(
					"suggestion_not_trigger",
					document,
					position,
					"multiple_line_Completion_setting_not_trigger",
					request,
				)
				return true
			}
			this.isImmediateTrigger = true
		} else {
			if (SPECIAL_TRIGGER_AFTER_CHAR.includes(cursorAfterChar)) {
				this.triggerSuggestionTriggerEvent(
					"suggestion_trigger",
					document,
					position,
					"after_cursor_special_char_trigger",
					request,
				)
				this.clipping = `${SUGGESTION_EVENT_MESSAGE["after_cursor_special_char_trigger"]}, 触发补全`
				return false
			} else if (cursorAfterChar.trim().length) {
				this.triggerSuggestionTriggerEvent(
					"suggestion_not_trigger",
					document,
					position,
					"after_cursor_char_not_trigger",
					request,
				)
				return true
			}
		}

		this.triggerSuggestionTriggerEvent("suggestion_trigger", document, position, "all_pass_trigger", request)
		this.clipping = `${SUGGESTION_EVENT_MESSAGE["all_pass_trigger"]}, 触发补全`
		return false
	}

	// quick suggestion上下文不变，继续用上次的代码片段, 返回数组本地构建出代码补全
	private shouldUseBackCompletion(position: vscode.Position, context: vscode.InlineCompletionContext): any[] {
		const theNewInlineCompletionItems: any[] = []

		// 已触发显示代码补全，用户继续输入且输入的代码段和代码补全存在开头重合，这时不触发补全
		if (this.inlineCompletionItemsBackup?.length) {
			const theContextRange = context.selectedCompletionInfo?.range
			for (let item of this.inlineCompletionItemsBackup) {
				const insertText = (item.insertText as string).slice(
					Math.abs(
						theContextRange!.end.character - this._context!.selectedCompletionInfo!.range.end.character,
					),
				)
				// console.log(4400, insertText);
				if (insertText) {
					theNewInlineCompletionItems.push({
						insertText,
						range: new vscode.Range(position, position),
						command: item.command,
					})
				}
			}
		}

		return theNewInlineCompletionItems
	}

	// quick suggestion enter或tab选定输入，继续用上次的代码片段, 返回数组本地构建出代码补全
	private enterUseBackCompletion(position: vscode.Position): any[] {
		const theNewInlineCompletionItems: any[] = []

		// 已触发显示代码补全，用户继续输入且输入的代码段和代码补全存在开头重合，这时不触发补全
		if (this.inlineCompletionItemsBackup?.length) {
			const theCompletionEnd = this.lastSelectedCompletionInfo?.text.slice(
				this.lastSelectedCompletionInfo.range.end.character -
					this.lastSelectedCompletionInfo.range.start.character,
			)
			// console.log(
			// 	88811,
			// 	JSON.parse(JSON.stringify(this.inlineCompletionItemsBackupShow)),
			// 	this.lastSelectedCompletionInfo,
			// 	theCompletionEnd,
			// )
			for (let item of this.inlineCompletionItemsBackupShow) {
				// console.log(88844, item, (item.insertText as string).startsWith(theCompletionEnd!));
				if ((item.insertText as string).startsWith(theCompletionEnd!)) {
					// console.log(88855)
					const insertText = (item.insertText as string).slice(theCompletionEnd?.length)
					if (insertText) {
						theNewInlineCompletionItems.push({
							insertText,
							range: new vscode.Range(position, position),
							command: item.command,
						})
					}
				} else if (
					this.lastSelectedCompletionInfo?.text &&
					(item.insertText as string).startsWith(this.lastSelectedCompletionInfo.text)
				) {
					// console.log(88866)
					const insertText = (item.insertText as string).slice(this.lastSelectedCompletionInfo?.text?.length)
					if (insertText) {
						theNewInlineCompletionItems.push({
							insertText,
							range: new vscode.Range(position, position),
							command: item.command,
						})
					}
				}
			}
		}

		return theNewInlineCompletionItems
	}

	private createCancellationTokenPromise<T>(
		token: vscode.CancellationToken,
		abortController: AbortController,
		defaultValue: T,
	): Promise<T> {
		return new Promise((resolve) => {
			token.onCancellationRequested(() => {
				abortController.abort()
				resolve(defaultValue)
			})
		})
	}

	public async provideInlineCompletionItems(
		document: vscode.TextDocument,
		position: vscode.Position,
		context: vscode.InlineCompletionContext,
		token: vscode.CancellationToken,
	): Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList | null | undefined> {
		const startTimestamp = Date.now()

		// 确保 ContextProxy 已初始化（这是获取认证信息必需的）
		if (!this.contextProxy.isInitialized) {
			await this.contextProxy.initialize()
		}

		// 可选地确保其他组件已初始化，但不阻塞基本功能
		if (!this._isInitialized) {
			// 在后台启动初始化，但不等待完成
			this.initialize().catch((error) => {
				console.error("AiProvider background initialization failed:", error)
			})
		}

		const theAls = this.contextProxy.getAsl()
		// console.log(1111, context.selectedCompletionInfo, context.triggerKind,this.isAccept, theAls);
		// 判定用户是否登录
		if (!theAls) {
			return
		}

		// 逐行接受补全
		if (this.isLineAccept && this.lineAcceptText.trim().length) {
			// console.log(4444556, position, this.lineAcceptText);
			this.isLineAccept = !!context.triggerKind
			this.isAccept = false
			this.inlineCompletionItems = [
				{
					insertText: this.lineAcceptText,
					range: new vscode.Range(position, position),
				},
			]
			this.inlineCompletionItemsBackup = this.inlineCompletionItems
			this.inlineCompletionItemsBackupShow = this.inlineCompletionItems
			return this.inlineCompletionItems
		}

		const request = uuidv4()

		if (this.isAccept) {
			this.isAccept = false
			this.triggerSuggestionTriggerEvent(
				"suggestion_not_trigger",
				document,
				position,
				"accept_completion_not_trigger",
				request,
			)
			return
		}

		if (this._debouncer) {
			clearTimeout(this._debouncer)
		}
		this.isImmediateTrigger = false
		// console.log(88822, this.lastSelectedCompletionInfo, this.lastTextDocumentChangeEvent, this.lastSelectedCompletionInfo?.text.slice(this.lastSelectedCompletionInfo.range.end.character - this.lastSelectedCompletionInfo.range.start.character));

		if (context.selectedCompletionInfo) {
			this.lastSelectedCompletionInfo = context.selectedCompletionInfo
			const range = context?.selectedCompletionInfo?.range
			const text = context?.selectedCompletionInfo?.text
			if (!range) {
				return
			}
			const prexfixText = document.getText(range)
			// console.log(4400, prexfixText)
			// 判断 quick suggestion补全上下文text是不是以已输入代码开头，如果不是即使获取到代码补全也显示不出来
			// 对自定义代码片段snippets的情况进行过滤，此时不触发代码补全
			if (!text?.startsWith(prexfixText)) {
				return
			}
		}

		// console.log(3333, this.isCtrlKTrigger, context.triggerKind === 0, this.inlineCompletionItems)
		// 代码补全触发规则： 用户行为触发、系统行为触发 最终触发次数唯一性处理
		if (
			context.triggerKind === 0 &&
			this.inlineCompletionItems &&
			this.inlineCompletionItems[0]?.range?.start.isEqual(position)
		) {
			// console.log(3344, context.triggerKind === 0, this.inlineCompletionItems)
			const theInlineCompletionItem = [...this.inlineCompletionItems]
			this.inlineCompletionItems = undefined
			return theInlineCompletionItem
		}

		if (!this.isCtrlKTrigger) {
			// console.log(788, context, this._context)
			if (this._context?.selectedCompletionInfo && context?.selectedCompletionInfo) {
				// quick suggestion补全上下文没有改变, 检测能不能使用历史代码补全
				if (
					(this._context?.selectedCompletionInfo?.text === context?.selectedCompletionInfo?.text &&
						context?.selectedCompletionInfo?.range?.start?.isEqual(
							this._context?.selectedCompletionInfo?.range?.start,
						)) ||
					(this.inlineCompletionItemsBackup[0]?.insertText &&
						(this.inlineCompletionItemsBackup[0]?.insertText as string).startsWith(
							context?.selectedCompletionInfo?.text,
						))
				) {
					const theNewInlineCompletionItems = this.shouldUseBackCompletion(position, context)
					// console.log(789, theNewInlineCompletionItems)
					if (theNewInlineCompletionItems.length) {
						// console.log(555222)
						this.inlineCompletionItemsBackupShow = theNewInlineCompletionItems
						return theNewInlineCompletionItems
					}
				}
			}

			// quick suggestion enter选定输入，继续用上次的代码片段, 返回数组本地构建出代码补全
			if (!context?.selectedCompletionInfo) {
				// console.log(
				// 	88822,
				// 	this.lastSelectedCompletionInfo,
				// 	this.lastTextDocumentChangeEvent,
				// 	this.lastSelectedCompletionInfo?.text.slice(
				// 		this.lastSelectedCompletionInfo.range.end.character -
				// 			this.lastSelectedCompletionInfo.range.start.character,
				// 	),
				// )
				if (
					this.lastSelectedCompletionInfo &&
					this.lastTextDocumentChangeEvent &&
					this.lastSelectedCompletionInfo?.text ===
						this.lastTextDocumentChangeEvent?.contentChanges?.[0]?.text &&
					this.lastSelectedCompletionInfo?.range?.isEqual(
						this.lastTextDocumentChangeEvent?.contentChanges?.[0]?.range,
					)
				) {
					if (context.triggerKind) {
						const theNewInlineCompletionItems = this.enterUseBackCompletion(position)
						this.inlineCompletionItemsBackupShow = theNewInlineCompletionItems
						this.inlineCompletionItemsBackupShowAutomatic = theNewInlineCompletionItems
						// console.log(88800, theNewInlineCompletionItems)
						return theNewInlineCompletionItems
					} else {
						if (!this.inlineCompletionItemsBackupShowAutomatic) {
							const theNewInlineCompletionItems = this.enterUseBackCompletion(position)
							this.inlineCompletionItemsBackupShow = theNewInlineCompletionItems
							// console.log(888998, theNewInlineCompletionItems)
						} else {
							this.inlineCompletionItemsBackupShowAutomatic = undefined
						}
						// console.log(88800, this.inlineCompletionItemsBackupShow)
						return this.inlineCompletionItemsBackupShow
					}
				}
			}

			if (this.shouldSkipCompletion(document, position, request)) {
				return
			}
		} else {
			this.isCtrlKTrigger = false
			this.isImmediateTrigger = true
			this.clipping = "用户Ctrl+K快捷键主动, 触发补全"
		}

		// 重置AbortController
		const abortController: AbortController = new AbortController()
		token.onCancellationRequested(() => abortController.abort())

		let quickSuggestionPrefix = ""
		const theContextRange = context.selectedCompletionInfo?.range
		if (theContextRange) {
			if (theContextRange.end.character > theContextRange.start.character) {
				quickSuggestionPrefix = context.selectedCompletionInfo.text.slice(
					theContextRange.end.character - theContextRange.start.character,
				)
			} else {
				return
			}
		}
		// 从湛卢的GlobalState中获取debounce_time配置
		const debounce_time = this.contextProxy.getValue("completionDebounceTime") ?? 500
		const timeout = this.isImmediateTrigger ? 0 : debounce_time - 150
		const beforePromises: Promise<any>[] = [
			this.buildRequestHeadersBody(document, position, quickSuggestionPrefix),
			importFunsTimeout(this.domSymbol, undefined, timeout),
			this.context.globalState.get("zhanlu.copyTextPlain") as Promise<any>,
			importDefinitionsService.gitDiffHeadFileTimeout(document.uri.fsPath, timeout),
			rootPathContextService.getRootPathSnippets(document, position, timeout),
		]

		this.isImmediateTrigger = false
		const inlineCompletionRequestPromise = () => {
			return new Promise<vscode.InlineCompletionItem[] | vscode.InlineCompletionList>((resolve, reject) => {
				return Promise.allSettled(beforePromises).then((theRequest: PromiseSettledResult<any>[]) => {
					// console.log(490, theRequest, Date.now() - startTimestamp)

					this.statusBar.showLoading()

					const { requestBody, requestHeaders } = (theRequest[0] as any).value
					// console.log(3333333444, context.selectedCompletionInfo, context.triggerKind, requestBody);
					const urlPath: string = "/api/acepilot/zhanlu/completions"
					const requestUrl = buildOpUrl(urlPath, theAls, this.contextProxy.getBaseUrl())
					let cross_files: any[] = [
						...this.activeEditorTracker.generateNeighbourFile(vscode.window.activeTextEditor!),
					]
					if (theRequest[1].status === "fulfilled" && theRequest[1].value?.length) {
						cross_files = [...cross_files, ...theRequest[1].value]
					}

					if (theRequest[2].status === "fulfilled" && theRequest[2].value?.copyTextPlain?.length) {
						cross_files.push({
							kind: "ClipboardSnippets",
							path: theRequest[2].value.fsPath,
							content: theRequest[2].value.copyTextPlain,
							depth: null,
						})
					}

					if (theRequest[3].status === "fulfilled" && theRequest[3].value?.length) {
						cross_files.push({
							kind: "DiffSnippet",
							path: document.uri.fsPath,
							content: theRequest[3].value,
							depth: null,
						})
					}

					if (theRequest[4].status === "fulfilled" && theRequest[4].value?.length) {
						cross_files = [...cross_files, ...theRequest[4].value]
					}

					if (theRequest[5] && theRequest[5].status === "fulfilled" && theRequest[5].value?.length) {
						const theCodeSearch = (theRequest[5].value as [])
							.filter((item: any) => item.score >= 0.5)
							.map((item: any) => ({
								content: item.code,
								depth: 1,
								kind: "RetrievalSnippet",
								path: document.uri.fsPath,
							}))
						if (theCodeSearch.length) {
							cross_files = [...cross_files, ...theCodeSearch]
						}
					}
					console.log("The CrossFiles of Http: ", cross_files)
					this.outputChannel.appendLine(
						`CompletionFacade cost time(从补全开始到获取完跨文件信息耗时): ${Date.now() - startTimestamp}ms`,
					)

					requestBody.inputs["cross_files"] = cross_files
					const theBody = {
						data: AESEncrypt(JSON.stringify(requestBody), theAls.token),
					}

					const theStartTme = Date.now()
					this.outputChannel.appendLine(
						`[InlineCompletionHandler] completion consume time(从补全开始到发送补全请求耗时): ${Date.now() - startTimestamp}ms`,
					)
					this.outputChannel.appendLine(`requestId---${request}`)
					fetch(requestUrl, {
						method: "post",
						headers: {
							"Content-Type": "application/json",
							plugin_version: this.context.extension?.packageJSON?.version ?? "",
							plugin_type: "vscode",
							request,
							...requestHeaders,
						},
						signal: abortController.signal,
						body: JSON.stringify(theBody),
					})
						.then((response) => response.json())
						.then((res: any) => {
							const theEndTime = Date.now()
							console.log("Completion Http Result(收到的补全结果):", res)
							this.outputChannel.appendLine(
								`Completion Http Cost Time(从发送补全请求到收到补全结果的耗时): ${theEndTime - theStartTme}ms`,
							)
							if (
								vscode.window.activeTextEditor?.selection?.active &&
								position.isEqual(vscode.window?.activeTextEditor?.selection?.active)
							) {
								const theRes = JSON.parse(AESDecrypt(res.data, theAls.token))
								this._context = context
								console.log("Completion Http Result(收到的补全结果解密后的数据):", theRes)
								// console.log(3333333, theRes, quickSuggestionPrefix)
								if (theRes?.choices?.length) {
									const range = new vscode.Range(position, position)
									if (quickSuggestionPrefix.length) {
										this.inlineCompletionItems = Array.from(
											new Set(theRes.choices.map((item: any) => item.text)),
										).map((item: any) => {
											const insertText = `${quickSuggestionPrefix}${item}`
											return {
												insertText,
												range,
												command: {
													command: "zhanlu.accept_code",
													title: "采纳代码提示反馈",
													arguments: [
														insertText,
														request,
														"suggestion_accepted",
														this.clipping,
													],
												},
											}
										})
									} else {
										this.inlineCompletionItems = Array.from(
											new Set(theRes.choices.map((item: any) => item.text)),
										)
											.filter((item: any) => item.trim().length)
											.map((item: any) => {
												const insertText = item
												return {
													insertText,
													range,
													command: {
														command: "zhanlu.accept_code",
														title: "采纳代码提示反馈",
														arguments: [
															insertText,
															request,
															"suggestion_accepted",
															this.clipping,
														],
													},
												}
											})
									}
								} else {
									this.inlineCompletionItems = []
								}

								this.inlineCompletionItemsBackup = this.inlineCompletionItems
								this.inlineCompletionItemsBackupShow = this.inlineCompletionItems
								this.theRequestIdBackup = request
								resolve(this.inlineCompletionItems)
								if (this.ctrlArrowDownDecorationType) {
									this.ctrlArrowDownDecorationType.dispose()
								}
								if (quickSuggestionPrefix.length) {
									if (this.inlineCompletionItems.length) {
										this.ctrlArrowDownDecorationType = generateCtrlArrowDownDecorationType(
											this.context,
											`${this.ctrlTabEnterContentText}，请求耗时${theEndTime - theStartTme}ms`,
										)
										vscode.window.activeTextEditor!.setDecorations(
											this.ctrlArrowDownDecorationType,
											[
												new vscode.Range(
													new vscode.Position(position.line, 0),
													new vscode.Position(position.line, 0),
												),
											],
										)
									}
								} else {
									if (
										this.inlineCompletionItems.length === 1 &&
										(this.inlineCompletionItems[0].insertText as string).split("\n").length > 1
									) {
										this.ctrlArrowDownDecorationType = generateCtrlArrowDownDecorationType(
											this.context,
											`${this.ctrlArrowDownContentText}，请求耗时${theEndTime - theStartTme}ms`,
										)
										vscode.window.activeTextEditor!.setDecorations(
											this.ctrlArrowDownDecorationType,
											[
												new vscode.Range(
													new vscode.Position(position.line, 0),
													new vscode.Position(position.line, 0),
												),
											],
										)
									} else if (this.inlineCompletionItems.length > 1) {
										if (
											(this.inlineCompletionItems[0].insertText as string).split("\n").length > 1
										) {
											this.ctrlArrowDownDecorationType = generateCtrlArrowDownDecorationType(
												this.context,
												`${this.ctrlTabBracketDownContentText}，请求耗时${theEndTime - theStartTme}ms`,
											)
											vscode.window.activeTextEditor!.setDecorations(
												this.ctrlArrowDownDecorationType,
												[
													new vscode.Range(
														new vscode.Position(position.line, 0),
														new vscode.Position(position.line, 0),
													),
												],
											)
										} else {
											this.ctrlArrowDownDecorationType = generateCtrlArrowDownDecorationType(
												this.context,
												`${this.ctrlTabBracketDownContentText}，请求耗时${theEndTime - theStartTme}ms`,
											)
											vscode.window.activeTextEditor!.setDecorations(
												this.ctrlArrowDownDecorationType,
												[
													new vscode.Range(
														new vscode.Position(position.line, 0),
														new vscode.Position(position.line, 0),
													),
												],
											)
										}
									}
								}

								vscode.commands.executeCommand(
									"zhanlu.accept_code",
									"",
									request,
									"suggestion_previewed",
									this.clipping,
								)
							}
						})
						.catch((_error) => {
							// const theEndTime = Date.now()
							// console.log(44448888, request, theEndTime, theEndTime - theStartTme, error)
							reject()
						})
						.finally(() => {
							this.statusBar.showEcloudIcon()
							// vscode.commands.executeCommand('editor.action.triggerSuggest');
						})
				})
			})
		}

		if (token.isCancellationRequested) {
			return []
		}

		return Promise.race([
			inlineCompletionRequestPromise(),
			this.createCancellationTokenPromise(token, abortController, []),
		])
	}
}
