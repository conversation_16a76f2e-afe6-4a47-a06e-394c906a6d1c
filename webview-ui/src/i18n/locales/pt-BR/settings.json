{"common": {"save": "<PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "reset": "Redefinir", "select": "Selecionar", "add": "Adicionar <PERSON>", "remove": "Remover"}, "header": {"title": "Configurações", "saveButtonTooltip": "<PERSON><PERSON>", "nothingChangedTooltip": "Nada alterado", "doneButtonTooltip": "Descartar alterações não salvas e fechar o painel de configurações"}, "unsavedChangesDialog": {"title": "Alterações não salvas", "description": "<PERSON><PERSON><PERSON> descar<PERSON> as alterações e continuar?", "cancelButton": "<PERSON><PERSON><PERSON>", "discardButton": "Descartar alterações"}, "sections": {"providers": "Provedores", "autoApprove": "Aprovação", "browser": "<PERSON><PERSON><PERSON><PERSON>", "checkpoints": "Checkpoints", "notifications": "Notificações", "contextManagement": "Contexto", "terminal": "Terminal", "prompts": "Prompts", "completion": "Completamento", "experimental": "Experimental", "language": "Idioma", "about": "Sobre o Zhanlu", "interface": "Interface"}, "developerMode": {"title": "<PERSON><PERSON>", "label": "Ativar modo desenvolvedor", "description": "O modo desenvolvedor fornece recursos avançados e opções de configuração, incluindo recursos experimentais, configurações do terminal, gerenciamento de prompts e muito mais."}, "prompts": {"description": "Configure prompts de suporte usados para ações rápidas como melhorar prompts, explicar código e corrigir problemas. Esses prompts aju<PERSON> o zhanlu a fornecer melhor assistência para tarefas comuns de desenvolvimento."}, "codeIndex": {"title": "Indexação de Código", "enableLabel": "Ativar Indexação de Código", "enableDescription": "Ative a indexação de código para pesquisa e compreensão de contexto aprimoradas", "providerLabel": "<PERSON><PERSON><PERSON> de <PERSON>s", "selectProviderPlaceholder": "<PERSON><PERSON><PERSON><PERSON>or", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "Chave de API:", "geminiApiKeyPlaceholder": "Digite sua chave de API do Gemini", "mistralProvider": "<PERSON><PERSON><PERSON>", "mistralApiKeyLabel": "Chave de API:", "mistralApiKeyPlaceholder": "Digite sua chave de API da Mistral", "openaiCompatibleProvider": "Compatível com OpenAI", "openAiKeyLabel": "Chave de API OpenAI", "openAiKeyPlaceholder": "Digite sua chave de API OpenAI", "openAiCompatibleBaseUrlLabel": "URL Base", "openAiCompatibleApiKeyLabel": "Chave de <PERSON>", "openAiCompatibleApiKeyPlaceholder": "Digite sua chave de API", "openAiCompatibleModelDimensionLabel": "Dimensão de Embedding:", "modelDimensionLabel": "Dimensão do Modelo", "openAiCompatibleModelDimensionPlaceholder": "ex., 1536", "openAiCompatibleModelDimensionDescription": "A dimensão de embedding (tamanho de <PERSON>ída) para seu modelo. Verifique a documentação do seu provedor para este valor. Valores comuns: 384, 768, 1536, 3072.", "modelLabel": "<PERSON><PERSON>", "selectModelPlaceholder": "Selecionar modelo", "ollamaUrlLabel": "URL Ollama:", "qdrantUrlLabel": "URL Qdrant", "qdrantKeyLabel": "<PERSON><PERSON>:", "startIndexingButton": "Iniciar", "clearIndexDataButton": "Limpar <PERSON>", "unsavedSettingsMessage": "Por favor, salve suas configurações antes de iniciar o processo de indexação.", "clearDataDialog": {"title": "Tem certeza?", "description": "Esta ação não pode ser desfeita. Isso excluirá permanentemente os dados de índice da sua base de código.", "cancelButton": "<PERSON><PERSON><PERSON>", "confirmButton": "Lim<PERSON>"}, "description": "Configure as configurações de indexação da base de código para habilitar a pesquisa semântica do seu projeto. <0>Saiba mais</0>", "statusTitle": "Status", "settingsTitle": "Configurações de Indexação", "disabledMessage": "A indexação da base de código está atualmente desativada. Ative-a nas configurações globais para configurar as opções de indexação.", "embedderProviderLabel": "<PERSON><PERSON><PERSON>", "modelPlaceholder": "Insira o nome do modelo", "selectModel": "Selecione um modelo", "ollamaBaseUrlLabel": "URL Base do Ollama", "qdrantApiKeyLabel": "<PERSON><PERSON> da <PERSON>", "qdrantApiKeyPlaceholder": "Insira sua chave da <PERSON> Qdrant (opcional)", "setupConfigLabel": "Configuração", "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "Falha ao salvar configurações", "modelDimensions": "({{dimension}} dimensões)", "saveSuccess": "Configurações salvas com sucesso", "saving": "Salvando...", "saveSettings": "<PERSON><PERSON>", "indexingStatuses": {"standby": "Em espera", "indexing": "Indexando", "indexed": "Indexado", "error": "Erro"}, "close": "<PERSON><PERSON><PERSON>", "validation": {"invalidQdrantUrl": "URL do Qdrant inválida", "invalidOllamaUrl": "URL do Ollama inválida", "invalidBaseUrl": "URL base inválida", "qdrantUrlRequired": "A URL do Qdrant é obrigatória", "openaiApiKeyRequired": "A chave de API da OpenAI é obrigatória", "modelSelectionRequired": "A seleção do modelo é obrigatória", "apiKeyRequired": "A chave de API é obrigatória", "modelIdRequired": "O ID do modelo é obrigatório", "modelDimensionRequired": "A dimensão do modelo é obrigatória", "geminiApiKeyRequired": "A chave de API do Gemini é obrigatória", "mistralApiKeyRequired": "A chave de API Mistral é obrigatória", "ollamaBaseUrlRequired": "A URL base do Ollama é obrigatória", "baseUrlRequired": "A URL base é obrigatória", "modelDimensionMinValue": "A dimensão do modelo deve ser maior que 0"}, "advancedConfigLabel": "Configuração Avançada", "searchMinScoreLabel": "Limite de pontuação de busca", "searchMinScoreDescription": "Pontuação mínima de similaridade (0.0-1.0) necessária para os resultados da busca. Valores mais baixos retornam mais resultados, mas podem ser menos relevantes. Valores mais altos retornam menos resultados, mas mais relevantes.", "searchMinScoreResetTooltip": "Redefinir para o valor padrão (0.4)", "searchMaxResultsLabel": "Resultados máximos de busca", "searchMaxResultsDescription": "Número máximo de resultados de busca a retornar ao consultar o índice de código. Valores mais altos fornecem mais contexto, mas podem incluir resultados menos relevantes.", "resetToDefault": "Redefinir para o padrão"}, "autoApprove": {"description": "Permitir que o zhanlu realize operações automaticamente sem exigir aprovação. Ative essas configurações apenas se confiar totalmente na IA e compreender os riscos de segurança associados.", "toggleAriaLabel": "Alternar aprovação automática", "disabledAriaLabel": "Aprovação automática desativada - selecione as opções primeiro", "readOnly": {"label": "Leitura", "description": "Quando ativado, o Zhanlu visualizará automaticamente o conteúdo do diretório e lerá arquivos sem que você precise clicar no botão Aprovar.", "outsideWorkspace": {"label": "Incluir arquivos fora do espaço de trabalho", "description": "Permitir que o <PERSON>hanlu leia arquivos fora do espaço de trabalho atual sem exigir aprovação."}}, "write": {"label": "Escrita", "description": "Criar e editar arquivos automaticamente sem exigir aprovação", "delayLabel": "Atraso após escritas para permitir que diagnósticos detectem problemas potenciais", "outsideWorkspace": {"label": "Incluir arquivos fora do espaço de trabalho", "description": "Permitir que o Zhanlu crie e edite arquivos fora do espaço de trabalho atual sem exigir aprovação."}, "protected": {"label": "Incluir arquivos protegidos", "description": "Permitir que o Zhanlu crie e edite arquivos protegidos (como .zhanluignore e arquivos de configuração .zhanlu/) sem exigir aprovação."}}, "browser": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Realizar ações do navegador automaticamente sem exigir aprovação. Nota: Aplica-se apenas quando o modelo suporta uso do computador"}, "retry": {"label": "Tentar novamente", "description": "Tentar novamente automaticamente requisições de API com falha quando o servidor retorna uma resposta de erro", "delayLabel": "Atraso antes de tentar novamente a requisição"}, "mcp": {"label": "MCP", "description": "Ativar aprovação automática de ferramentas MCP individuais na visualização de Servidores MCP (requer tanto esta configuração quanto a caixa de seleção \"Permitir sempre\" da ferramenta)"}, "modeSwitch": {"label": "Modo", "description": "Alternar automaticamente entre diferentes modos sem exigir aprovação"}, "subtasks": {"label": "Subtarefas", "description": "Permitir a criação e conclusão de subtarefas sem exigir aprovação"}, "followupQuestions": {"label": "<PERSON><PERSON><PERSON>", "description": "Selecionar automaticamente a primeira resposta sugerida para perguntas de acompanhamento após o tempo limite configurado", "timeoutLabel": "Tempo de espera antes de selecionar automaticamente a primeira resposta"}, "execute": {"label": "Executar", "description": "Executar automaticamente comandos de terminal permitidos sem exigir aprovação", "allowedCommands": "Comandos de auto-execução permitidos", "allowedCommandsDescription": "Prefixos de comando que podem ser auto-executados quando \"Aprovar sempre operações de execução\" está ativado. Adicione * para permitir todos os comandos (use com cautela).", "deniedCommands": "<PERSON><PERSON><PERSON> negado<PERSON>", "deniedCommandsDescription": "Prefixos de comandos que serão automaticamente negados sem pedir aprovação. Em caso de conflitos com comandos permitidos, a correspondência de prefixo mais longa tem precedência. Adicione * para negar todos os comandos.", "commandPlaceholder": "Digite o prefixo do comando (ex. 'git ')", "deniedCommandPlaceholder": "Digite o prefixo do comando para negar (ex. 'rm -rf')", "addButton": "<PERSON><PERSON><PERSON><PERSON>", "autoDenied": "Comandos com o prefixo `{{prefix}}` foram proibidos pelo usuário. Não contorne esta restrição executando outro comando."}, "updateTodoList": {"label": "Todo", "description": "A lista de tarefas é atualizada automaticamente sem aprovação"}, "apiRequestLimit": {"title": "Máximo de Solicitações", "description": "Fazer automaticamente este número de requisições à API antes de pedir aprovação para continuar com a tarefa.", "unlimited": "<PERSON><PERSON><PERSON><PERSON>"}, "selectOptionsFirst": "Selecione pelo menos uma opção abaixo para habilitar a aprovação automática"}, "providers": {"providerDocumentation": "Documentação do {{provider}}", "configProfile": "Perfil de configuração", "description": "Salve diferentes configurações de API para alternar rapidamente entre provedores e configurações.", "apiProvider": "Provedor de API", "model": "<PERSON><PERSON>", "nameEmpty": "O nome não pode estar vazio", "nameExists": "Já existe um perfil com este nome", "nameTooLong": "O nome não pode ter mais de 20 caracteres", "deleteProfile": "Excluir perfil", "invalidArnFormat": "Formato de ARN inválido. Verifique os exemplos acima.", "enterNewName": "Digite um novo nome", "addProfile": "<PERSON><PERSON><PERSON><PERSON> perfil", "renameProfile": "Renomear perfil", "newProfile": "Novo perfil de configuração", "enterProfileName": "Digite o nome do perfil", "createProfile": "<PERSON><PERSON><PERSON> perfil", "cannotDeleteOnlyProfile": "Não é possível excluir o único perfil", "searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>s", "searchProviderPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "noProviderMatchFound": "<PERSON><PERSON><PERSON> provedor encontrado", "noMatchFound": "Nenhum perfil correspondente encontrado", "vscodeLmDescription": "A API do Modelo de Linguagem do VS Code permite executar modelos fornecidos por outras extensões do VS Code (incluindo, mas não se limitando, ao GitHub Copilot). A maneira mais fácil de começar é instalar as extensões Copilot e Copilot Chat no VS Code Marketplace.", "awsCustomArnUse": "Insira um ARN Amazon Bedrock válido para o modelo que deseja usar. Exemplos de formato:", "awsCustomArnDesc": "Certifique-se de que a região no ARN corresponde à região AWS selecionada acima.", "openRouterApiKey": "Chave de API OpenRouter", "getOpenRouterApiKey": "Obter chave de API OpenRouter", "apiKeyStorageNotice": "As chaves de API são armazenadas com segurança no Armazenamento Secreto do VSCode", "glamaApiKey": "Chave de API Glama", "getGlamaApiKey": "Obter chave de API Glama", "useCustomBaseUrl": "Usar URL base personalizado", "useReasoning": "Habilitar rac<PERSON>io", "useHostHeader": "Usar cabeçalho Host personalizado", "useLegacyFormat": "Usar formato de API OpenAI legado", "customHeaders": "Cabeçalhos personalizados", "headerName": "Nome do cabeçalho", "headerValue": "Valor do cabeçalho", "noCustomHeaders": "Nenhum cabeçalho personalizado definido. Clique no botão + para adicionar um.", "requestyApiKey": "Chave de API Requesty", "refreshModels": {"label": "Atualizar modelos", "hint": "Por favor, re<PERSON><PERSON> as configurações para ver os modelos mais recentes.", "loading": "Atualizando lista de modelos...", "success": "Lista de modelos atualizada com sucesso!", "error": "Falha ao atualizar a lista de modelos. Por favor, tente novamente."}, "getRequestyApiKey": "Obter chave de API Requesty", "openRouterTransformsText": "Comprimir prompts e cadeias de mensagens para o tamanho do contexto (<a>Transformações OpenRouter</a>)", "anthropicApiKey": "<PERSON>ve de <PERSON>", "getAnthropicApiKey": "Obter chave de <PERSON>", "anthropicUseAuthToken": "Passar a chave de API Anthropic como cabeçalho Authorization em vez de X-Api-Key", "chutesApiKey": "Chave de API Chutes", "getChutesApiKey": "Obter chave de API Chutes", "deepSeekApiKey": "Chave de API DeepSeek", "getDeepSeekApiKey": "Obter chave de API DeepSeek", "moonshotApiKey": "Chave de <PERSON> Moonshot", "getMoonshotApiKey": "Obter chave de API Moonshot", "moonshotBaseUrl": "Ponto de entrada Moonshot", "geminiApiKey": "Chave de API Gemini", "getGroqApiKey": "Obter chave de API Groq", "groqApiKey": "Chave de API Groq", "getGeminiApiKey": "Obter chave de API Gemini", "getHuggingFaceApiKey": "Obter chave de API Hugging Face", "huggingFaceApiKey": "Chave de API Hugging Face", "huggingFaceModelId": "ID do modelo", "huggingFaceLoading": "Carregando...", "huggingFaceModelsCount": "({{count}} modelos)", "huggingFaceSelectModel": "Selecionar um modelo...", "huggingFaceSearchModels": "Buscar modelos...", "huggingFaceNoModelsFound": "Nenhum modelo encontrado", "huggingFaceProvider": "<PERSON><PERSON><PERSON>", "huggingFaceProviderAuto": "Automático", "huggingFaceSelectProvider": "Selecionar um provedor...", "huggingFaceSearchProviders": "Buscar provedores...", "huggingFaceNoProvidersFound": "<PERSON><PERSON><PERSON> provedor encontrado", "apiKey": "Chave de <PERSON>", "openAiApiKey": "Chave de API OpenAI", "openAiBaseUrl": "URL Base", "getOpenAiApiKey": "Obter chave de API OpenAI", "mistralApiKey": "Chave de API Mistral", "getMistralApiKey": "Obter chave de API Mistral / Codestral", "codestralBaseUrl": "URL Base Codestral (Opcional)", "codestralBaseUrlDesc": "Defina uma URL alternativa para o modelo Codestral.", "xaiApiKey": "Chave de API xAI", "getXaiApiKey": "Obter chave de API xAI", "litellmApiKey": "Chave API LiteLLM", "litellmBaseUrl": "URL base LiteLLM", "awsCredentials": "Credenciais AWS", "awsProfile": "Perfil AWS", "awsApiKey": "Chave de API Amazon Bedrock", "awsProfileName": "Nome do Perfil AWS", "awsAccessKey": "Chave de Acesso AWS", "awsSecretKey": "Chave Secreta AWS", "awsSessionToken": "Token de Sessão AWS", "awsRegion": "Região AWS", "awsCrossRegion": "Usar inferência entre regiões", "awsBedrockVpc": {"useCustomVpcEndpoint": "Usar endpoint VPC personalizado", "vpcEndpointUrlPlaceholder": "Digite a URL do endpoint VPC (opcional)", "examples": "Exemplos:"}, "enablePromptCaching": "Ativar cache de prompts", "enablePromptCachingTitle": "Ativar cache de prompts para melhorar o desempenho e reduzir custos para modelos suportados.", "cacheUsageNote": "Nota: Se você não vir o uso do cache, tente selecionar um modelo diferente e depois selecionar novamente o modelo desejado.", "vscodeLmModel": "Modelo de Linguagem", "vscodeLmWarning": "Nota: Esta é uma integração muito experimental e o suporte do provedor pode variar. Se você receber um erro sobre um modelo não ser suportado, isso é um problema do lado do provedor.", "googleCloudSetup": {"title": "Para usar o Google Cloud Vertex AI, você precisa:", "step1": "1. Criar uma conta Google Cloud, ativar a API Vertex AI e ativar os modelos Zhanlu desejados.", "step2": "2. Instalar o CLI do Google Cloud e configurar as credenciais padrão do aplicativo.", "step3": "3. <PERSON>u criar uma conta de serviço com credenciais."}, "googleCloudCredentials": "Credenciais Google Cloud", "googleCloudKeyFile": "Caminho do Arquivo de Chave Google Cloud", "googleCloudProjectId": "ID do Projeto Google Cloud", "googleCloudRegion": "Região Google Cloud", "lmStudio": {"baseUrl": "URL Base (opcional)", "modelId": "ID do Modelo", "speculativeDecoding": "Ativar Decodificação Especulativa", "draftModelId": "ID do Modelo de Rascunho", "draftModelDesc": "O modelo de rascunho deve ser da mesma família de modelos para que a decodificação especulativa funcione corretamente.", "selectDraftModel": "Selecionar Modelo de Rascunho", "noModelsFound": "Nenhum modelo de rascunho encontrado. Certifique-se de que o LM Studio esteja em execução com o Modo Servidor ativado.", "description": "O LM Studio permite que você execute modelos localmente em seu computador. Para instruções sobre como começar, veja o <a>guia de início r<PERSON>pido</a> deles. Você também precisará iniciar o recurso de <b>servidor local</b> do LM Studio para usá-lo com esta extensão. <span>Nota:</span> O Zhanlu usa prompts complexos e funciona melhor com modelos Zhanlu. Modelos menos capazes podem não funcionar como esperado."}, "ollama": {"baseUrl": "URL Base (opcional)", "modelId": "ID do Modelo", "description": "O Ollama permite que você execute modelos localmente em seu computador. Para instruções sobre como começar, veja o guia de início rápido del<PERSON>.", "warning": "Nota: <PERSON>lu usa prompts complexos e funciona melhor com modelos Z<PERSON>lu. Modelos menos capazes podem não funcionar como esperado."}, "unboundApiKey": "Chave de API Unbound", "getUnboundApiKey": "Obter chave de API Unbound", "unboundRefreshModelsSuccess": "Lista de modelos atualizada! Agora você pode selecionar entre os modelos mais recentes.", "unboundInvalidApiKey": "Chave API inválida. Por favor, verifique sua chave API e tente novamente.", "humanRelay": {"description": "Não é necessária chave de API, mas o usuário precisa ajudar a copiar e colar as informações para a IA do chat web.", "instructions": "Durante o uso, uma caixa de diálogo será exibida e a mensagem atual será copiada para a área de transferência automaticamente. Você precisa colar isso nas versões web de IA (como Zhanlu), depois copiar a resposta da IA de volta para a caixa de diálogo e clicar no botão confirmar."}, "openRouter": {"providerRouting": {"title": "Roteamento de Provedores OpenRouter", "description": "OpenRouter direciona solicitações para os melhores provedores disponíveis para seu modelo. <PERSON><PERSON> padr<PERSON>, as solicitações são balanceadas entre os principais provedores para maximizar o tempo de atividade. No entanto, você pode escolher um provedor específico para usar com este modelo.", "learnMore": "Saiba mais sobre roteamento de provedores"}}, "customModel": {"capabilities": "Configure as capacidades e preços para seu modelo personalizado compatível com OpenAI. Tenha cuidado ao especificar as capacidades do modelo, pois elas podem afetar como o Zhanlu funciona.", "maxTokens": {"label": "Máximo de Tokens de Saída", "description": "Número máximo de tokens que o modelo pode gerar em uma resposta. (Especifique -1 para permitir que o servidor defina o máximo de tokens.)"}, "contextWindow": {"label": "<PERSON><PERSON><PERSON> Contexto", "description": "Total de tokens (entrada + saída) que o modelo pode processar."}, "imageSupport": {"label": "Suporte a Imagens", "description": "Este modelo é capaz de processar e entender imagens?"}, "computerUse": {"label": "Uso do Computador", "description": "Este modelo é capaz de interagir com um navegador? (ex. <PERSON><PERSON><PERSON>)."}, "promptCache": {"label": "<PERSON><PERSON> de Prompts", "description": "Este modelo é capaz de fazer cache de prompts?"}, "pricing": {"input": {"label": "Preço de Entrada", "description": "Custo por milhão de tokens na entrada/prompt. <PERSON><PERSON> afeta o custo de enviar contexto e instruções para o modelo."}, "output": {"label": "Preço de Saída", "description": "Custo por milhão de tokens na resposta do modelo. <PERSON><PERSON> afeta o custo do conteúdo gerado e das conclusões."}, "cacheReads": {"label": "Preço de Leituras de Cache", "description": "Custo por milhão de tokens para leitura do cache. Este é o preço cobrado quando uma resposta em cache é recuperada."}, "cacheWrites": {"label": "Preço de Escritas de Cache", "description": "Custo por milhão de tokens para escrita no cache. Este é o preço cobrado quando um prompt é armazenado em cache pela primeira vez."}}, "resetDefaults": "<PERSON><PERSON><PERSON>"}, "rateLimitSeconds": {"label": "Limite de taxa", "description": "Tempo mínimo entre requisições de API."}, "consecutiveMistakeLimit": {"label": "Limite de Erros e Repetições", "description": "Número de erros consecutivos ou ações repetidas antes de exibir o diálogo 'Roo está com problemas'", "unlimitedDescription": "Tentativas ilimitadas ativadas (prosseguimento automático). O diálogo nunca aparecerá.", "warning": "⚠️ Definir como 0 permite tentativas ilimitadas, o que pode consumir um uso significativo da API"}, "reasoningEffort": {"label": "Esforço de raciocínio do modelo", "high": "Alto", "medium": "Médio", "low": "Baixo"}, "setReasoningLevel": "Habilitar esforço de raciocínio", "claudeCode": {"pathLabel": "<PERSON>in<PERSON> <PERSON>", "description": "Caminho opcional para o seu Claude Code CLI. O padrão é 'claude' se não for definido.", "placeholder": "Padrão: claude", "maxTokensLabel": "Tokens de saída máximos", "maxTokensDescription": "Número máximo de tokens de saída para respostas do Claude Code. O padrão é 8000."}}, "browser": {"enable": {"label": "Ativar ferramenta de nave<PERSON>r", "description": "<PERSON>uando ativado, o <PERSON><PERSON><PERSON> pode usar um navegador para interagir com sites ao usar modelos que suportam o uso do computador."}, "viewport": {"label": "<PERSON><PERSON><PERSON> da viewport", "description": "Selecione o tamanho da viewport para interações do navegador. Isso afeta como os sites são exibidos e como se interage com eles.", "options": {"largeDesktop": "Desktop grande (1280x800)", "smallDesktop": "Desktop pequeno (900x600)", "tablet": "Tablet (768x1024)", "mobile": "<PERSON><PERSON><PERSON> (360x640)"}}, "screenshotQuality": {"label": "Qualidade das capturas de tela", "description": "Ajuste a qualidade WebP das capturas de tela do navegador. Valores mais altos fornecem capturas mais nítidas, mas aumentam o uso de token."}, "remote": {"label": "Usar conexão remota de navegador", "description": "Conectar a um navegador Chrome executando com depuração remota ativada (--remote-debugging-port=9222).", "urlPlaceholder": "URL personalizado (ex. http://localhost:9222)", "testButton": "<PERSON><PERSON>", "testingButton": "Testando...", "instructions": "Digite o endereço do host do protocolo DevTools ou deixe em branco para descobrir automaticamente instâncias locais do Chrome. O botão Testar Conexão tentará usar o URL personalizado, se fornecido, ou descobrirá automaticamente se o campo estiver vazio."}}, "checkpoints": {"enable": {"label": "Ativar pontos de verificação automáticos", "description": "<PERSON>uando ativado, o Zhanlu criará automaticamente pontos de verificação durante a execução de tarefas, facilitando a revisão de alterações ou o retorno a estados anteriores."}}, "notifications": {"sound": {"label": "Ativar efeitos sonoros", "description": "<PERSON>uando at<PERSON>, o Zhanlu reproduzirá efeitos sonoros para notificações e eventos.", "volumeLabel": "Volume"}, "tts": {"label": "Ativar texto para fala", "description": "<PERSON>uando ativado, o <PERSON> lerá em voz alta suas respostas usando texto para fala.", "speedLabel": "Velocidade"}}, "contextManagement": {"description": "Controle quais informações são incluídas na janela de contexto da IA, afetando o uso de token e a qualidade da resposta", "autoCondenseContextPercent": {"label": "Limite para acionar a condensação inteligente de contexto", "description": "Quando a janela de contexto atingir este limite, o zhanlu a condensará automaticamente."}, "condensingApiConfiguration": {"label": "Configuração de API para Condensação de Contexto", "description": "Selecione qual configuração de API usar para operações de condensação de contexto. Deixe desmarcado para usar a configuração ativa atual.", "useCurrentConfig": "Padrão"}, "customCondensingPrompt": {"label": "Prompt Personalizado de Condensação de Contexto", "description": "Prompt de sistema personalizado para condensação de contexto. Deixe em branco para usar o prompt padrão.", "placeholder": "Digite seu prompt de condensação personalizado aqui...\n\nVocê pode usar a mesma estrutura do prompt padrão:\n- Conversa Anterior\n- Trabalho Atual\n- Conceitos Técnicos Principais\n- Arquivos e Código Relevantes\n- Resolução de Problemas\n- Tarefas Pendentes e Próximos Passos", "reset": "<PERSON><PERSON><PERSON>", "hint": "Vazio = usar prompt padrão"}, "autoCondenseContext": {"name": "Acionar automaticamente a condensação inteligente de contexto", "description": "Quando habilitado, o zhanlu condensará automaticamente o contexto quando o limite for atingido. Quando desabilitado, você ainda pode acionar manualmente a condensação de contexto."}, "openTabs": {"label": "Limite de contexto de abas abertas", "description": "Número máximo de abas abertas do VSCode a incluir no contexto. Valores mais altos fornecem mais contexto, mas aumentam o uso de token."}, "workspaceFiles": {"label": "Limite de contexto de arquivos do espaço de trabalho", "description": "Número máximo de arquivos a incluir nos detalhes do diretório de trabalho atual. Valores mais altos fornecem mais contexto, mas aumentam o uso de token."}, "rooignore": {"label": "Mostrar arquivos .zhanluignore em listas e pesquisas", "description": "Quando ativado, os arquivos que correspondem aos padrões em .zhanluignore serão mostrados em listas com um símbolo de cadeado. Quando desativado, esses arquivos serão completamente ocultos das listas de arquivos e pesquisas."}, "maxReadFile": {"label": "Limite de auto-truncamento de leitura de arquivo", "description": "O Zhanlu lê este número de linhas quando o modelo omite valores de início/fim. Se este número for menor que o total do arquivo, o Zhanlu gera um índice de números de linha das definições de código. Casos especiais: -1 instrui o Zhanlu a ler o arquivo inteiro (sem indexação), e 0 instrui a não ler linhas e fornecer apenas índices de linha para contexto mínimo. Valores mais baixos minimizam o uso inicial de contexto, permitindo leituras posteriores precisas de intervalos de linhas. Requisições com início/fim explícitos não são limitadas por esta configuração.", "lines": "linhas", "always_full_read": "Sempre ler o arquivo inteiro"}, "maxConcurrentFileReads": {"label": "Limite de leituras simultâneas", "description": "Número máximo de arquivos que a ferramenta 'read_file' pode processar simultaneamente. Valores mais altos podem acelerar a leitura de vários arquivos pequenos, mas aumentam o uso de memória."}, "diagnostics": {"includeMessages": {"label": "Incluir diagnósticos automaticamente no contexto", "description": "Quando ativado, mensagens de diagnóstico (erros) de arquivos editados serão automaticamente incluídas no contexto. Você sempre pode incluir manualmente todos os diagnósticos do espaço de trabalho usando @problems."}, "maxMessages": {"label": "Máximo de mensagens de diagnóstico", "description": "Número máximo de mensagens de diagnóstico a serem incluídas por arquivo. Este limite se aplica tanto à inclusão automática (quando a caixa de seleção está ativada) quanto às menções manuais de @problems. Valores mais altos fornecem mais contexto, mas aumentam o uso de tokens.", "resetTooltip": "Redefinir para o valor padrão (50)", "unlimited": "Mensagens de diagnóstico ilimitadas", "unlimitedLabel": "<PERSON><PERSON><PERSON><PERSON>"}, "delayAfterWrite": {"label": "Atraso a<PERSON> as gravações para permitir que os diagnósticos detectem possíveis problemas", "description": "Tempo de espera após a gravação de arquivos antes de prosseguir, permitindo que as ferramentas de diagnóstico processem as alterações e detectem problemas."}}, "condensingThreshold": {"label": "Limite de Ativação de Condensação", "selectProfile": "Configurar limite para perfil", "defaultProfile": "Padrão Global (todos os perfis)", "defaultDescription": "Quando o contexto atingir essa porcentagem, será automaticamente condensado para todos os perfis, a menos que tenham configurações personalizadas", "profileDescription": "Limite personalizado apenas para este perfil (substitui o padrão global)", "inheritDescription": "Este perfil herda o limite padrão global ({{threshold}}%)", "usesGlobal": "(usa global {{threshold}}%)"}}, "terminal": {"basic": {"label": "Configurações do terminal: Básicas", "description": "Configurações básicas do terminal"}, "advanced": {"label": "Configurações do terminal: Avançadas", "description": "As seguintes opções podem exigir reiniciar o terminal para aplicar a configuração."}, "outputLineLimit": {"label": "Limite de saída do terminal", "description": "Número máximo de linhas a incluir na saída do terminal ao executar comandos. Quando excedido, as linhas serão removidas do meio, economizando token. <0>Saiba mais</0>"}, "outputCharacterLimit": {"label": "Limite de caracteres do terminal", "description": "Número máximo de caracteres a serem incluídos na saída do terminal ao executar comandos. Este limite tem precedência sobre o limite de linhas para evitar problemas de memória com linhas extremamente longas. Quando excedido, a saída será truncada. <0><PERSON><PERSON> mais</0>"}, "shellIntegrationTimeout": {"label": "Tempo limite de integração do shell do terminal", "description": "Tempo máximo de espera para a inicialização da integração do shell antes de executar comandos. Para usuários com tempos de inicialização de shell longos, este valor pode precisar ser aumentado se você vir erros \"Shell Integration Unavailable\" no terminal. <0>Sai<PERSON> mais</0>"}, "shellIntegrationDisabled": {"label": "Desativar integração do shell do terminal", "description": "Ative isso se os comandos do terminal não estiverem funcionando corretamente ou se você vir erros de 'Shell Integration Unavailable'. Isso usa um método mais simples para executar comandos, ignorando alguns recursos avançados do terminal. <0>Saiba mais</0>"}, "commandDelay": {"label": "Atraso de comando do terminal", "description": "Atraso em milissegundos para adicionar após a execução do comando. A configuração padrão de 0 desativa completamente o atraso. Isso pode ajudar a garantir que a saída do comando seja totalmente capturada em terminais com problemas de temporização. Na maioria dos terminais, isso é implementado definindo `PROMPT_COMMAND='sleep N'` e o PowerShell adiciona `start-sleep` ao final de cada comando. Originalmente era uma solução para o bug VSCode#237208 e pode não ser necessário. <0><PERSON><PERSON> mais</0>"}, "compressProgressBar": {"label": "Comprimir saída de barras de progresso", "description": "Quando ativado, processa a saída do terminal com retornos de carro (\\r) para simular como um terminal real exibiria o conteúdo. <PERSON><PERSON> remove os estados intermediários das barras de progresso, mantendo apenas o estado final, o que conserva espaço de contexto para informações mais relevantes. <0>Saiba mais</0>"}, "powershellCounter": {"label": "Ativar solução alternativa do contador PowerShell", "description": "Quando ativado, adiciona um contador aos comandos PowerShell para garantir a execução correta dos comandos. Isso ajuda com terminais PowerShell que podem ter problemas com a captura de saída. <0>Sai<PERSON> mais</0>"}, "zshClearEolMark": {"label": "Limpar marca de fim de linha do ZSH", "description": "Quando ativado, limpa a marca de fim de linha do ZSH definindo PROMPT_EOL_MARK=''. Isso evita problemas com a interpretação da saída de comandos quando termina com caracteres especiais como '%'. <0>Saiba mais</0>"}, "zshOhMy": {"label": "Ativar integração Oh My Zsh", "description": "Quando ativado, define ITERM_SHELL_INTEGRATION_INSTALLED=Yes para habilitar os recursos de integração do shell Oh My Zsh. A aplicação desta configuração pode exigir a reinicialização do IDE. <0>Saiba mais</0>"}, "zshP10k": {"label": "Ativar integração Powerlevel10k", "description": "Quando ativado, define POWERLEVEL9K_TERM_SHELL_INTEGRATION=true para habilitar os recursos de integração do shell Powerlevel10k. <0><PERSON><PERSON> mais</0>"}, "zdotdir": {"label": "Ativar gerenciamento do ZDOTDIR", "description": "Quando ativado, cria um diretório temporário para o ZDOTDIR para lidar corretamente com a integração do shell zsh. Isso garante que a integração do shell do VSCode funcione corretamente com o zsh enquanto preserva sua configuração do zsh. <0>Saiba mais</0>"}, "inheritEnv": {"label": "<PERSON><PERSON> var<PERSON> de ambiente", "description": "<PERSON>uando ativado, o terminal herda variáveis de ambiente do processo pai do VSCode, como configurações de integração do shell definidas no perfil do usuário. Isso alterna diretamente a configuração global do VSCode `terminal.integrated.inheritEnv`. <0>Saiba mais</0>"}}, "advancedSettings": {"title": "Configurações avançadas"}, "advanced": {"diff": {"label": "Ativar edição através de diffs", "description": "<PERSON>uando ativado, o Zhanlu poderá editar arquivos mais rapidamente e rejeitará automaticamente escritas completas de arquivos truncados. Funciona melhor com o modelo mais recente Zhanlu.", "strategy": {"label": "Estratégia de diff", "options": {"standard": "Padrão (Bloco único)", "multiBlock": "Experimental: Diff multi-bloco", "unified": "Experimental: <PERSON>ff unificado"}, "descriptions": {"standard": "A estratégia de diff padrão aplica alterações a um único bloco de código por vez.", "unified": "A estratégia de diff unificado adota várias abordagens para aplicar diffs e escolhe a melhor abordagem.", "multiBlock": "A estratégia de diff multi-bloco permite atualizar vários blocos de código em um arquivo em uma única requisição."}}, "matchPrecision": {"label": "Precisão de correspondência", "description": "Este controle deslizante controla quão precisamente as seções de código devem corresponder ao aplicar diffs. Valores mais baixos permitem correspondências mais flexíveis, mas aumentam o risco de substituições incorretas. Use valores abaixo de 100% com extrema cautela."}}, "todoList": {"label": "Habilitar ferramenta de lista de tarefas", "description": "<PERSON>uando habilitado, o zhanlu pode criar e gerenciar listas de tarefas para acompanhar o progresso das tarefas. Isso ajuda a organizar tarefas complexas em etapas gerenciáveis."}}, "completion": {"description": "Configure as configurações de completamento de código para melhorar sua experiência de desenvolvimento.", "configureButton": "Configurar"}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Usar estratégia diff unificada experimental", "description": "Ativar a estratégia diff unificada experimental. Esta estratégia pode reduzir o número de novas tentativas causadas por erros do modelo, mas pode causar comportamento inesperado ou edições incorretas. Ative apenas se compreender os riscos e estiver disposto a revisar cuidadosamente todas as alterações."}, "SEARCH_AND_REPLACE": {"name": "Usar ferramenta de busca e substituição experimental", "description": "Ativar a ferramenta de busca e substituição experimental, permitindo que o Zhanlu substitua várias instâncias de um termo de busca em uma única solicitação."}, "INSERT_BLOCK": {"name": "Usar ferramenta de inserção de conteúdo experimental", "description": "Ativar a ferramenta de inserção de conteúdo experimental, permitindo que o Zhanlu insira conteúdo em números de linha específicos sem precisar criar um diff."}, "POWER_STEERING": {"name": "Usar modo \"direção assistida\" experimental", "description": "<PERSON>uando ativado, o Z<PERSON>lu lembrará o modelo sobre os detalhes da sua definição de modo atual com mais frequência. Isso levará a uma adesão mais forte às definições de função e instruções personalizadas, mas usará mais tokens por mensagem."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Usar ferramenta diff de múltiplos blocos experimental", "description": "<PERSON>uando ativado, o Zhanlu usará a ferramenta diff de múltiplos blocos. Isso tentará atualizar vários blocos de código no arquivo em uma única solicitação."}, "CONCURRENT_FILE_READS": {"name": "Habilitar leitura simultânea de arquivos", "description": "Quando habilitado, o zhanlu pode ler vários arquivos em uma única solicitação. Quando desabilitado, o zhanlu deve ler arquivos um de cada vez. Desabilitar pode ajudar ao trabalhar com modelos menos capazes ou quando você deseja mais controle sobre o acesso aos arquivos."}, "MARKETPLACE": {"name": "Ativar Marketplace", "description": "<PERSON>uando ativado, você pode instalar MCPs e modos personalizados do Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Habilitar edições de arquivos concorrentes", "description": "Quando habilitado, o zhanlu pode editar múltiplos arquivos em uma única solicitação. Quando desabilitado, o zhanlu deve editar arquivos um de cada vez. Desabilitar isso pode ajudar ao trabalhar com modelos menos capazes ou quando você quer mais controle sobre modificações de arquivos."}}, "promptCaching": {"label": "Desativar cache de prompts", "description": "<PERSON>uan<PERSON> marcado, o Zhanlu não usará o cache de prompts para este modelo."}, "temperature": {"useCustom": "Usar temperatura personalizada", "description": "Controla a aleatoriedade nas respostas do modelo.", "rangeDescription": "Valores mais altos tornam a saída mais aleatória, valores mais baixos a tornam mais determinística."}, "modelInfo": {"supportsImages": "Suporta imagens", "noImages": "Não suporta imagens", "supportsComputerUse": "Suporta uso do computador", "noComputerUse": "Não suporta uso do computador", "supportsPromptCache": "Suporta cache de prompts", "noPromptCache": "Não suporta cache de prompts", "maxOutput": "<PERSON><PERSON><PERSON>", "inputPrice": "Preço de entrada", "outputPrice": "Preço de saída", "cacheReadsPrice": "Preço de leituras de cache", "cacheWritesPrice": "Preço de escritas de cache", "enableStreaming": "Ativar streaming", "enableR1Format": "Ativar parâmetros do modelo R1", "enableR1FormatTips": "Deve ser ativado ao usar modelos R1 como QWQ, para evitar erro 400", "useAzure": "Usar Azure", "azureApiVersion": "Definir versão da API Azure", "gemini": {"freeRequests": "* Gratuito até {{count}} requisições por minuto. De<PERSON><PERSON> disso, a cobrança depende do tamanho do prompt.", "pricingDetails": "Para mais informações, consulte os detalhes de preços.", "billingEstimate": "* A cobrança é uma estimativa - o custo exato depende do tamanho do prompt."}}, "modelPicker": {"automaticFetch": "A extensão busca automaticamente a lista mais recente de modelos disponíveis em <serviceLink>{{serviceName}}</serviceLink>. Se você não tem certeza sobre qual modelo escolher, o Zhanlu funciona melhor com <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Você também pode pesquisar por \"free\" para encontrar opções gratuitas atualmente disponíveis.", "label": "<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "noMatchFound": "Nenhuma correspondência encontrada", "useCustomModel": "Usar personalizado: {{modelId}}"}, "footer": {"feedback": "Se você tiver alguma dúvida ou feedback, sinta-se à vontade para <qqDocsLink>abrir uma questão</qqDocsLink>.", "version": "<PERSON><PERSON><PERSON> v{{version}}", "telemetry": {"label": "Permitir relatórios anônimos de erros e uso", "description": "Ajude a melhorar o Zhanlu enviando dados de uso anônimos e relatórios de erros. Nunca são enviados código, prompts ou informações pessoais. Consulte nossa política de privacidade para mais detalhes."}, "settings": {"import": "Importar", "export": "Exportar", "reset": "Redefinir"}}, "thinkingBudget": {"maxTokens": "Tokens máximos", "maxThinkingTokens": "Tokens máximos de pensamento"}, "validation": {"apiKey": "Você deve fornecer uma chave de API válida.", "awsRegion": "Você deve escolher uma região para usar o Amazon Bedrock.", "googleCloud": "Você deve fornecer um ID de projeto e região do Google Cloud válidos.", "modelId": "Você deve fornecer um ID de modelo válido.", "modelSelector": "Você deve fornecer um seletor de modelo válido.", "openAi": "Você deve fornecer uma URL base, chave de API e ID de modelo válidos.", "arn": {"invalidFormat": "Formato de ARN inválido. Por favor, verifique os requisitos de formato.", "regionMismatch": "Aviso: A região em seu ARN ({{arnRegion}}) não corresponde à região selecionada ({{region}}). Isso pode causar problemas de acesso. O provedor usará a região do ARN."}, "modelAvailability": "O ID do modelo ({{modelId}}) que você forneceu não está disponível. Por favor, escolha outro modelo.", "providerNotAllowed": "O provedor '{{provider}}' não é permitido pela sua organização", "modelNotAllowed": "O modelo '{{model}}' não é permitido para o provedor '{{provider}}' pela sua organização", "profileInvalid": "Este perfil contém um provedor ou modelo que não é permitido pela sua organização"}, "placeholders": {"apiKey": "Digite a chave API...", "profileName": "Digite o nome do perfil", "accessKey": "Digite a chave de acesso...", "secretKey": "Digite a chave secreta...", "sessionToken": "Digite o token de sessão...", "credentialsJson": "Digite o JSON de credenciais...", "keyFilePath": "Digite o caminho do arquivo de chave...", "projectId": "Digite o ID do projeto...", "customArn": "Digite o ARN (ex: arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Digite a URL base...", "modelId": {"lmStudio": "ex: meta-llama-3.1-8b-instruct", "lmStudioDraft": "ex: lmstudio-community/llama-3.2-1b-instruct", "ollama": "ex: llama3.1"}, "numbers": {"maxTokens": "ex: 4096", "contextWindow": "ex: 128000", "inputPrice": "ex: 0.0001", "outputPrice": "ex: 0.0002", "cacheWritePrice": "ex: 0.00005"}}, "defaults": {"ollamaUrl": "Padrão: http://localhost:11434", "lmStudioUrl": "Padrão: http://localhost:1234", "geminiUrl": "Padrão: https://generativelanguage.googleapis.com"}, "labels": {"customArn": "ARN personalizado", "useCustomArn": "Usar ARN personalizado..."}, "includeMaxOutputTokens": "Incluir tokens máximos de saída", "includeMaxOutputTokensDescription": "Enviar parâmetro de tokens máximos de saída nas solicitações de API. Alguns provedores podem não suportar isso.", "limitMaxTokensDescription": "Limitar o número máximo de tokens na resposta", "maxOutputTokensLabel": "Tokens máximos de saída", "maxTokensGenerateDescription": "Tokens máximos para gerar na resposta"}