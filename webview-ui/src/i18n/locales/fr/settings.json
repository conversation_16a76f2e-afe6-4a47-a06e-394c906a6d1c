{"common": {"save": "Enregistrer", "done": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "reset": "Réinitialiser", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "add": "A<PERSON>ter un en-tête", "remove": "<PERSON><PERSON><PERSON><PERSON>"}, "header": {"title": "Paramètres", "saveButtonTooltip": "Enregistrer les modifications", "nothingChangedTooltip": "<PERSON><PERSON> n'a changé", "doneButtonTooltip": "Ignorer les modifications non enregistrées et fermer le panneau des paramètres"}, "unsavedChangesDialog": {"title": "Modifications non enregistrées", "description": "V<PERSON><PERSON><PERSON>-vous ignorer les modifications et continuer ?", "cancelButton": "Annuler", "discardButton": "Ignorer les modifications"}, "sections": {"providers": "Fournisseurs", "autoApprove": "Auto-approbation", "browser": "Accès ordinateur", "checkpoints": "Points de contrôle", "notifications": "Notifications", "contextManagement": "Contexte", "terminal": "Terminal", "prompts": "<PERSON><PERSON><PERSON>", "completion": "Complétion", "experimental": "Expérimental", "language": "<PERSON><PERSON>", "about": "À propos de Zhanlu", "interface": "Interface"}, "developerMode": {"title": "Mode développeur", "label": "Activer le mode développeur", "description": "Le mode développeur fournit des fonctionnalités avancées et des options de configuration, y compris des fonctionnalités expérimentales, des paramètres de terminal, la gestion des invites et plus encore."}, "prompts": {"description": "Configurez les invites de support utilisées pour les actions rapides comme l'amélioration des invites, l'explication du code et la résolution des problèmes. <PERSON><PERSON> invites aident <PERSON><PERSON><PERSON> à fournir une meilleure assistance pour les tâches de développement courantes."}, "codeIndex": {"title": "Indexation de la base de code", "description": "Configurez les paramètres d'indexation de la base de code pour activer la recherche sémantique dans votre projet. <0>En savoir plus</0>", "statusTitle": "Statut", "enableLabel": "Activer l'indexation de la base de code", "enableDescription": "Activer l'indexation du code pour une recherche et une compréhension du contexte améliorées", "settingsTitle": "Paramètres d'indexation", "disabledMessage": "L'indexation de la base de code est actuellement désactivée. Activez-la dans les paramètres globaux pour configurer les options d'indexation.", "providerLabel": "Fournis<PERSON>ur d'embeddings", "embedderProviderLabel": "Fournis<PERSON>ur d'embedder", "selectProviderPlaceholder": "Sélectionner un fournisseur", "openaiProvider": "OpenAI", "ollamaProvider": "Ollama", "geminiProvider": "Gemini", "geminiApiKeyLabel": "Clé API :", "geminiApiKeyPlaceholder": "Entrez votre clé API Gemini", "mistralProvider": "<PERSON><PERSON><PERSON>", "mistralApiKeyLabel": "Clé d'API:", "mistralApiKeyPlaceholder": "Entrez votre clé d'API Mistral", "openaiCompatibleProvider": "Compatible OpenAI", "openAiKeyLabel": "Clé API OpenAI", "openAiKeyPlaceholder": "Entrez votre clé API OpenAI", "openAiCompatibleBaseUrlLabel": "URL de base", "openAiCompatibleApiKeyLabel": "Clé API", "openAiCompatibleApiKeyPlaceholder": "Entrez votre clé API", "openAiCompatibleModelDimensionLabel": "Dimension d'Embedding :", "modelDimensionLabel": "Dimension du modèle", "openAiCompatibleModelDimensionPlaceholder": "ex., 1536", "openAiCompatibleModelDimensionDescription": "La dimension d'embedding (taille de sortie) pour votre modèle. Consultez la documentation de votre fournisseur pour cette valeur. Valeurs courantes : 384, 768, 1536, 3072.", "modelLabel": "<PERSON><PERSON><PERSON><PERSON>", "modelPlaceholder": "Entrez le nom du modèle", "selectModel": "Sélectionner un modèle", "selectModelPlaceholder": "Sélectionner un modèle", "ollamaUrlLabel": "URL Ollama :", "ollamaBaseUrlLabel": "URL de base Ollama", "qdrantUrlLabel": "URL Qdrant", "qdrantKeyLabel": "<PERSON><PERSON> Qdrant :", "qdrantApiKeyLabel": "Clé API Qdrant", "qdrantApiKeyPlaceholder": "Entrez votre clé API Qdrant (optionnel)", "setupConfigLabel": "Configuration", "startIndexingButton": "<PERSON><PERSON><PERSON><PERSON>", "clearIndexDataButton": "Effacer l'index", "unsavedSettingsMessage": "Merci d'enregistrer tes paramètres avant de démarrer le processus d'indexation.", "clearDataDialog": {"title": "Êtes-vous sûr ?", "description": "Cette action ne peut pas être annulée. Cela supprimera définitivement les données d'index de votre base de code.", "cancelButton": "Annuler", "confirmButton": "Efface<PERSON> les données"}, "ollamaUrlPlaceholder": "http://localhost:11434", "openAiCompatibleBaseUrlPlaceholder": "https://api.example.com", "modelDimensionPlaceholder": "1536", "qdrantUrlPlaceholder": "http://localhost:6333", "saveError": "Échec de la sauvegarde des paramètres", "modelDimensions": "({{dimension}} dimensions)", "saveSuccess": "Paramètres sauvegardés avec succès", "saving": "Sauvegarde...", "saveSettings": "<PERSON><PERSON><PERSON><PERSON>", "indexingStatuses": {"standby": "En attente", "indexing": "Indexation", "indexed": "Indexé", "error": "<PERSON><PERSON><PERSON>"}, "close": "<PERSON><PERSON><PERSON>", "validation": {"invalidQdrantUrl": "URL Qdrant invalide", "invalidOllamaUrl": "URL Ollama invalide", "invalidBaseUrl": "URL de base invalide", "qdrantUrlRequired": "L'URL Qdrant est requise", "openaiApiKeyRequired": "La clé API OpenAI est requise", "modelSelectionRequired": "La sélection du modèle est requise", "apiKeyRequired": "La clé API est requise", "modelIdRequired": "L'ID du modèle est requis", "modelDimensionRequired": "La dimension du modèle est requise", "geminiApiKeyRequired": "La clé API Gemini est requise", "mistralApiKeyRequired": "La clé API Mistral est requise", "ollamaBaseUrlRequired": "L'URL de base Ollama est requise", "baseUrlRequired": "L'URL de base est requise", "modelDimensionMinValue": "La dimension du modèle doit être supérieure à 0"}, "advancedConfigLabel": "Configuration avancée", "searchMinScoreLabel": "Seuil de score de recherche", "searchMinScoreDescription": "Score de similarité minimum (0.0-1.0) requis pour les résultats de recherche. Des valeurs plus faibles renvoient plus de résultats mais peuvent être moins pertinents. Des valeurs plus élevées renvoient moins de résultats mais plus pertinents.", "searchMinScoreResetTooltip": "Réinitialiser à la valeur par défaut (0.4)", "searchMaxResultsLabel": "Résultats de recherche maximum", "searchMaxResultsDescription": "Nombre maximum de résultats de recherche à retourner lors de l'interrogation de l'index de code. Des valeurs plus élevées fournissent plus de contexte mais peuvent inclure des résultats moins pertinents.", "resetToDefault": "Réinitialiser par défaut"}, "autoApprove": {"description": "Permettre à zhanlu d'effectuer automatiquement des opérations sans requérir d'approbation. Activez ces paramètres uniquement si vous faites entièrement confiance à l'IA et que vous comprenez les risques de sécurité associés.", "toggleAriaLabel": "Activer/désactiver l'approbation automatique", "disabledAriaLabel": "Approbation automatique désactivée - sélectionnez d'abord les options", "selectOptionsFirst": "Sélectionnez au moins une option ci-dessous pour activer l'approbation automatique", "readOnly": {"label": "Lecture", "description": "Lorsque cette option est activée, Zhanlu affichera automatiquement le contenu des répertoires et lira les fichiers sans que vous ayez à cliquer sur le bouton Approuver.", "outsideWorkspace": {"label": "Inclure les fichiers en dehors de l'espace de travail", "description": "Permettre à Zhanlu de lire des fichiers en dehors de l'espace de travail actuel sans nécessiter d'approbation."}}, "write": {"label": "Écriture", "description": "Créer et modifier automatiquement des fichiers sans nécessiter d'approbation", "delayLabel": "<PERSON><PERSON><PERSON> après les écritures pour permettre aux diagnostics de détecter les problèmes potentiels", "outsideWorkspace": {"label": "Inclure les fichiers en dehors de l'espace de travail", "description": "Permettre à Zhanlu de créer et modifier des fichiers en dehors de l'espace de travail actuel sans nécessiter d'approbation."}, "protected": {"label": "Inclure les fichiers protégés", "description": "Permettre à Zhanlu de créer et modifier des fichiers protégés (comme .zhanluignore et les fichiers de configuration .zhanlu/) sans nécessiter d'approbation."}}, "browser": {"label": "Navigateur", "description": "Effectuer automatiquement des actions du navigateur sans nécessiter d'approbation. Remarque : S'applique uniquement lorsque le modèle prend en charge l'utilisation de l'ordinateur"}, "retry": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "Réessayer automatiquement les requêtes API échouées lorsque le serveur renvoie une réponse d'erreur", "delayLabel": "<PERSON><PERSON><PERSON> réessayer la requête"}, "mcp": {"label": "MCP", "description": "Activer l'approbation automatique des outils MCP individuels dans la vue des serveurs MCP (nécessite à la fois ce paramètre et la case à cocher \"Toujours autoriser\" de l'outil)"}, "modeSwitch": {"label": "Mode", "description": "Basculer automatiquement entre différents modes sans nécessiter d'approbation"}, "subtasks": {"label": "Sous-tâches", "description": "Permettre la création et l'achèvement des sous-tâches sans nécessiter d'approbation"}, "followupQuestions": {"label": "Question", "description": "Sélectionner automatiquement la première réponse suggérée pour les questions de suivi après le délai configuré", "timeoutLabel": "Temps d'attente avant la sélection automatique de la première réponse"}, "execute": {"label": "Exécuter", "description": "Exécuter automatiquement les commandes de terminal autorisées sans nécessiter d'approbation", "allowedCommands": "Commandes auto-exécutables autorisées", "allowedCommandsDescription": "Préfixes de commandes qui peuvent être auto-exécutés lorsque \"Toujours approuver les opérations d'exécution\" est activé. Ajoutez * pour autoriser toutes les commandes (à utiliser avec précaution).", "deniedCommands": "Commandes refusées", "deniedCommandsDescription": "Préfixes de commandes qui seront automatiquement refusés sans demander d'approbation. En cas de conflit avec les commandes autorisées, la correspondance de préfixe la plus longue prend la priorité. Ajoutez * pour refuser toutes les commandes.", "commandPlaceholder": "<PERSON><PERSON><PERSON> le préfixe de commande (ex. 'git ')", "deniedCommandPlaceholder": "Entrez le préfixe de commande à refuser (ex. 'rm -rf')", "addButton": "Ajouter", "autoDenied": "Les commandes avec le préfixe `{{prefix}}` ont été interdites par l'utilisateur. Ne contourne pas cette restriction en exécutant une autre commande."}, "updateTodoList": {"label": "Todo", "description": "La liste de tâches est mise à jour automatiquement sans approbation"}, "apiRequestLimit": {"title": "Requêtes maximales", "description": "Effectuer automatiquement ce nombre de requêtes API avant de demander l'approbation pour continuer la tâche.", "unlimited": "Illimité"}}, "providers": {"providerDocumentation": "Documentation {{provider}}", "configProfile": "Profil de configuration", "description": "Enregistrez différentes configurations d'API pour basculer rapidement entre les fournisseurs et les paramètres.", "apiProvider": "Fournisseur d'API", "model": "<PERSON><PERSON><PERSON><PERSON>", "nameEmpty": "Le nom ne peut pas être vide", "nameExists": "Un profil avec ce nom existe déjà", "nameTooLong": "Le nom ne peut pas dépasser 20 caractères", "deleteProfile": "Supp<PERSON>er le profil", "invalidArnFormat": "Format ARN invalide. Veuillez vérifier les exemples ci-dessus.", "enterNewName": "Entrez un nouveau nom", "addProfile": "Ajouter un profil", "renameProfile": "<PERSON>mmer le profil", "newProfile": "Nouveau profil de configuration", "enterProfileName": "Entrez le nom du profil", "createProfile": "<PERSON><PERSON>er un profil", "cannotDeleteOnlyProfile": "Impossible de supprimer le seul profil", "searchPlaceholder": "Rechercher des profils", "searchProviderPlaceholder": "Rechercher des fournisseurs", "noProviderMatchFound": "<PERSON><PERSON><PERSON> fournis<PERSON>ur trouvé", "noMatchFound": "<PERSON><PERSON>n profil correspondant trouvé", "vscodeLmDescription": "L'API du modèle de langage VS Code vous permet d'exécuter des modèles fournis par d'autres extensions VS Code (y compris, mais sans s'y limiter, GitHub Copilot). Le moyen le plus simple de commencer est d'installer les extensions Copilot et Copilot Chat depuis le VS Code Marketplace.", "awsCustomArnUse": "Entrez un ARN Amazon Bedrock valide pour le modèle que vous souhaitez utiliser. Exemples de format :", "awsCustomArnDesc": "Assurez-vous que la région dans l'ARN correspond à la région AWS sélectionnée ci-dessus.", "openRouterApiKey": "Clé API OpenRouter", "getOpenRouterApiKey": "Obtenir la clé API OpenRouter", "apiKeyStorageNotice": "Les clés API sont stockées en toute sécurité dans le stockage sécurisé de VSCode", "glamaApiKey": "Clé API Glama", "getGlamaApiKey": "Obtenir la clé API Glama", "useCustomBaseUrl": "Utiliser une URL de base personnalisée", "useReasoning": "<PERSON><PERSON> le raisonnement", "useHostHeader": "Utiliser un en-tête Host personnalisé", "useLegacyFormat": "Utiliser le format API OpenAI hérité", "customHeaders": "<PERSON>-t<PERSON><PERSON> person<PERSON>", "headerName": "Nom de l'en-tête", "headerValue": "<PERSON>ur de l'en-tête", "noCustomHeaders": "Aucun en-tête personnalisé défini. Cliquez sur le bouton + pour en ajouter un.", "requestyApiKey": "Clé API Requesty", "refreshModels": {"label": "Actualiser les modèles", "hint": "Veuillez rouvrir les paramètres pour voir les modèles les plus récents.", "loading": "Actualisation de la liste des modèles...", "success": "Liste des modèles actualisée avec succès !", "error": "Échec de l'actualisation de la liste des modèles. Veuillez réessayer."}, "getRequestyApiKey": "Obtenir la clé API Requesty", "openRouterTransformsText": "Compresser les prompts et chaînes de messages à la taille du contexte (<a>Transformations OpenRouter</a>)", "anthropicApiKey": "Clé API Zhanlu", "getAnthropicApiKey": "Obtenir la clé API <PERSON>", "anthropicUseAuthToken": "Passer la clé API Anthropic comme en-tête d'autorisation au lieu de X-Api-Key", "chutesApiKey": "Clé API Chutes", "getChutesApiKey": "Obtenir la clé API Chutes", "deepSeekApiKey": "Clé API DeepSeek", "getDeepSeekApiKey": "Obtenir la clé API DeepSeek", "moonshotApiKey": "Clé API Moonshot", "getMoonshotApiKey": "Obtenir la clé API Moonshot", "moonshotBaseUrl": "Point d'entrée Moonshot", "geminiApiKey": "Clé API Gemini", "getGroqApiKey": "Obtenir la clé API Groq", "groqApiKey": "Clé API Groq", "getHuggingFaceApiKey": "Obtenir la clé API Hugging Face", "huggingFaceApiKey": "Clé API Hugging Face", "huggingFaceModelId": "ID du modèle", "huggingFaceLoading": "Chargement...", "huggingFaceModelsCount": "({{count}} modèles)", "huggingFaceSelectModel": "Sélectionner un modèle...", "huggingFaceSearchModels": "Rechercher des modèles...", "huggingFaceNoModelsFound": "<PERSON><PERSON><PERSON> mod<PERSON>le trouvé", "huggingFaceProvider": "Fournisseur", "huggingFaceProviderAuto": "Automatique", "huggingFaceSelectProvider": "Sélectionner un fournisseur...", "huggingFaceSearchProviders": "Rechercher des fournisseurs...", "huggingFaceNoProvidersFound": "<PERSON><PERSON><PERSON> fournis<PERSON>ur trouvé", "getGeminiApiKey": "Obtenir la clé API Gemini", "openAiApiKey": "Clé API OpenAI", "apiKey": "Clé API", "openAiBaseUrl": "URL de base", "getOpenAiApiKey": "Obtenir la clé API OpenAI", "mistralApiKey": "Clé API Mistral", "getMistralApiKey": "Obtenir la clé API Mistral / Codestral", "codestralBaseUrl": "URL de base Codestral (Optionnel)", "codestralBaseUrlDesc": "Définir une URL alternative pour le modèle Codestral.", "xaiApiKey": "Clé API xAI", "getXaiApiKey": "Obtenir la clé API xAI", "litellmApiKey": "Clé API LiteLLM", "litellmBaseUrl": "URL de base LiteLLM", "awsCredentials": "Identifiants AWS", "awsProfile": "Profil AWS", "awsApiKey": "Clé API Amazon Bedrock", "awsProfileName": "Nom du profil AWS", "awsAccessKey": "Clé d'accès AWS", "awsSecretKey": "Clé secrète AWS", "awsSessionToken": "Jeton de session AWS", "awsRegion": "Région AWS", "awsCrossRegion": "Utiliser l'inférence inter-régions", "awsBedrockVpc": {"useCustomVpcEndpoint": "Utiliser un point de terminaison VPC personnalisé", "vpcEndpointUrlPlaceholder": "Entrer l'URL du point de terminaison VPC (optionnel)", "examples": "Exemples :"}, "enablePromptCaching": "Activer la mise en cache des prompts", "enablePromptCachingTitle": "Activer la mise en cache des prompts pour améliorer les performances et réduire les coûts pour les modèles pris en charge.", "cacheUsageNote": "Remarque : Si vous ne voyez pas l'utilisation du cache, essayez de sélectionner un modèle différent puis de sélectionner à nouveau votre modèle souhaité.", "vscodeLmModel": "<PERSON><PERSON><PERSON><PERSON>", "vscodeLmWarning": "Remarque : Il s'agit d'une intégration très expérimentale et le support des fournisseurs variera. Si vous recevez une erreur concernant un modèle non pris en charge, c'est un problème du côté du fournisseur.", "googleCloudSetup": {"title": "Pour utiliser Google Cloud Vertex AI, vous devez :", "step1": "1. Créer un compte Google Cloud, activer l'API Vertex AI et activer les modèles Zhanlu souhaités.", "step2": "2. Installer Google Cloud CLI et configurer les identifiants par défaut de l'application.", "step3": "3. O<PERSON> créer un compte de service avec des identifiants."}, "googleCloudCredentials": "Identifiants Google Cloud", "googleCloudKeyFile": "Chemin du fichier de clé Google Cloud", "googleCloudProjectId": "ID du projet Google Cloud", "googleCloudRegion": "Région Google Cloud", "lmStudio": {"baseUrl": "URL de base (optionnel)", "modelId": "ID du modèle", "speculativeDecoding": "Activer le décodage spéculatif", "draftModelId": "ID du modèle brouillon", "draftModelDesc": "Le modèle brouillon doit être de la même famille de modèles pour que le décodage spéculatif fonctionne correctement.", "selectDraftModel": "Sélectionner le modèle brouillon", "noModelsFound": "Aucun modèle brouillon trouvé. Veuillez vous assurer que LM Studio est en cours d'exécution avec le mode serveur activé.", "description": "LM Studio vous permet d'exécuter des modèles localement sur votre ordinateur. Pour obtenir des instructions sur la mise en route, consultez leur <a>guide de démarrage rapide</a>. Vous devrez également démarrer la fonction <b>serveur local</b> de LM Studio pour l'utiliser avec cette extension. <span>Remarque :</span> Zhanlu utilise des prompts complexes et fonctionne mieux avec les modèles Zhanlu. Les modèles moins performants peuvent ne pas fonctionner comme prévu."}, "ollama": {"baseUrl": "URL de base (optionnel)", "modelId": "ID du modèle", "description": "Ollama vous permet d'exécuter des modèles localement sur votre ordinateur. Pour obtenir des instructions sur la mise en route, consultez le guide de démarrage rapide.", "warning": "Remarque : <PERSON><PERSON><PERSON> utilise des prompts complexes et fonctionne mieux avec les modèles Zhanlu. Les modèles moins performants peuvent ne pas fonctionner comme prévu."}, "unboundApiKey": "Clé API Unbound", "getUnboundApiKey": "Obtenir la clé API Unbound", "unboundRefreshModelsSuccess": "Liste des modèles mise à jour ! Vous pouvez maintenant sélectionner parmi les derniers modèles.", "unboundInvalidApiKey": "Clé API invalide. Veuillez vérifier votre clé API et réessayer.", "humanRelay": {"description": "Aucune clé API n'est requise, mais l'utilisateur doit aider à copier et coller les informations dans le chat web de l'IA.", "instructions": "Pendant l'utilisation, une boîte de dialogue apparaîtra et le message actuel sera automatiquement copié dans le presse-papiers. Vous devez le coller dans les versions web de l'IA (comme Zhanlu), puis copier la réponse de l'IA dans la boîte de dialogue et cliquer sur le bouton de confirmation."}, "openRouter": {"providerRouting": {"title": "Routage des fournisseurs OpenRouter", "description": "OpenRouter dirige les requêtes vers les meilleurs fournisseurs disponibles pour votre modèle. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, les requêtes sont équilibrées entre les principaux fournisseurs pour maximiser la disponibilité. Cependant, vous pouvez choisir un fournisseur spécifique à utiliser pour ce modèle.", "learnMore": "En savoir plus sur le routage des fournisseurs"}}, "customModel": {"capabilities": "Configurez les capacités et les prix pour votre modèle personnalisé compatible OpenAI. Soyez prudent lors de la spécification des capacités du modèle, car elles peuvent affecter le fonctionnement de Zhanlu.", "maxTokens": {"label": "Tokens de sortie maximum", "description": "Nombre maximum de tokens que le modèle peut générer dans une réponse. (Spécifiez -1 pour permettre au serveur de définir les tokens maximum.)"}, "contextWindow": {"label": "<PERSON><PERSON> de la fenêtre de contexte", "description": "Total des tokens (entrée + sortie) que le modèle peut traiter."}, "imageSupport": {"label": "Support des images", "description": "Ce modèle est-il capable de traiter et de comprendre les images ?"}, "computerUse": {"label": "Utilisation de l'ordinateur", "description": "Ce modèle est-il capable d'interagir avec un navigateur ? (ex. <PERSON><PERSON><PERSON>)"}, "promptCache": {"label": "Mise en cache des prompts", "description": "Ce modèle est-il capable de mettre en cache les prompts ?"}, "pricing": {"input": {"label": "Prix d'entrée", "description": "Coût par million de tokens dans l'entrée/prompt. <PERSON>la affecte le coût d'envoi du contexte et des instructions au modèle."}, "output": {"label": "Prix de sortie", "description": "Coût par million de tokens dans la réponse du modèle. Cela affecte le coût du contenu généré et des complétions."}, "cacheReads": {"label": "Prix des lectures de cache", "description": "Coût par million de tokens pour la lecture depuis le cache. C'est le prix facturé lors de la récupération d'une réponse mise en cache."}, "cacheWrites": {"label": "Prix des écritures de cache", "description": "Coût par million de tokens pour l'écriture dans le cache. C'est le prix facturé lors de la première mise en cache d'un prompt."}}, "resetDefaults": "Réinitialiser les valeurs par défaut"}, "rateLimitSeconds": {"label": "<PERSON><PERSON>", "description": "Temps minimum entre les requêtes API."}, "consecutiveMistakeLimit": {"label": "Limite d'erreurs et de répétitions", "description": "Nombre d'erreurs consécutives ou d'actions répétées avant d'afficher la boîte de dialogue 'Roo a des difficultés'", "unlimitedDescription": "Réessais illimités activés (poursuite automatique). La boîte de dialogue n'apparaîtra jamais.", "warning": "⚠️ Mettre à 0 autorise des réessais illimités, ce qui peut consommer une utilisation importante de l'API"}, "reasoningEffort": {"label": "Effort de raisonnement du modèle", "high": "<PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "low": "Faible"}, "setReasoningLevel": "Activer l'effort de raisonnement", "claudeCode": {"pathLabel": "Chemin du code <PERSON>", "description": "Chemin facultatif vers votre CLI Claude Code. La valeur par défaut est 'claude' si non défini.", "placeholder": "Défaut : claude", "maxTokensLabel": "Jetons de sortie max", "maxTokensDescription": "Nombre maximum de jetons de sortie pour les réponses de Claude Code. La valeur par défaut est 8000."}}, "browser": {"enable": {"label": "<PERSON>r l'outil de navigateur", "description": "Lorsque cette option est activée, Zhanlu peut utiliser un navigateur pour interagir avec des sites web lors de l'utilisation de modèles qui prennent en charge l'utilisation de l'ordinateur."}, "viewport": {"label": "<PERSON><PERSON> de la fenêtre d'affichage", "description": "Sélectionnez la taille de la fenêtre d'affichage pour les interactions du navigateur. Cela affecte la façon dont les sites web sont affichés et dont on interagit avec eux.", "options": {"largeDesktop": "Grand bureau (1280x800)", "smallDesktop": "Petit bureau (900x600)", "tablet": "Tablette (768x1024)", "mobile": "Mobile (360x640)"}}, "screenshotQuality": {"label": "Qualité des captures d'écran", "description": "Ajustez la qualité WebP des captures d'écran du navigateur. Des valeurs plus élevées fournissent des captures plus claires mais augmentent l'utilisation de token."}, "remote": {"label": "Utiliser une connexion de navigateur distant", "description": "Se connecter à un navigateur Chrome exécuté avec le débogage à distance activé (--remote-debugging-port=9222).", "urlPlaceholder": "URL personnalisée (ex. http://localhost:9222)", "testButton": "Tester la connexion", "testingButton": "Test en cours...", "instructions": "Entrez l'adresse hôte du protocole DevTools ou laissez vide pour découvrir automatiquement les instances Chrome locales. Le bouton Tester la connexion essaiera l'URL personnalisée si fournie, ou découvrira automatiquement si le champ est vide."}}, "checkpoints": {"enable": {"label": "Activer les points de contrôle automatiques", "description": "Lorsque cette option est activée, Zhanlu créera automatiquement des points de contrôle pendant l'exécution des tâches, facilitant la révision des modifications ou le retour à des états antérieurs."}}, "notifications": {"sound": {"label": "<PERSON>r les effets sonores", "description": "Lorsque cette option est activée, <PERSON><PERSON><PERSON> jouera des effets sonores pour les notifications et les événements.", "volumeLabel": "Volume"}, "tts": {"label": "Activer la synthèse vocale", "description": "Lorsque cette option est activée, <PERSON><PERSON><PERSON> lira ses réponses à haute voix en utilisant la synthèse vocale.", "speedLabel": "Vitesse"}}, "contextManagement": {"description": "Contrôlez quelles informations sont incluses dans la fenêtre de contexte de l'IA, affectant l'utilisation de token et la qualité des réponses", "autoCondenseContextPercent": {"label": "Seuil de déclenchement de la condensation intelligente du contexte", "description": "Lorsque la fenêtre de contexte atteint ce seuil, z<PERSON><PERSON> la condensera automatiquement."}, "condensingApiConfiguration": {"label": "Configuration API pour la condensation du contexte", "description": "Sélectionnez quelle configuration API utiliser pour les opérations de condensation du contexte. Laissez non sélectionné pour utiliser la configuration active actuelle.", "useCurrentConfig": "<PERSON><PERSON> <PERSON><PERSON>"}, "customCondensingPrompt": {"label": "Prompt personnalisé de condensation du contexte", "description": "Personnalisez le prompt système utilisé pour la condensation du contexte. Laissez vide pour utiliser le prompt par défaut.", "placeholder": "Entrez votre prompt de condensation personnalisé ici...\n\nVous pouvez utiliser la même structure que le prompt par défaut :\n- Conversation précédente\n- Travail en cours\n- Concepts techniques clés\n- Fichiers et code pertinents\n- Résolution de problèmes\n- Tâches en attente et prochaines étapes", "reset": "Réinitialiser par défaut", "hint": "Vide = utiliser le prompt par défaut"}, "autoCondenseContext": {"name": "Déclencher automatiquement la condensation intelligente du contexte", "description": "Lorsque cette option est activée, zhanlu condensera automatiquement le contexte lorsque le seuil est atteint. Lorsqu'elle est désactivée, vous pouvez toujours déclencher manuellement la condensation du contexte."}, "openTabs": {"label": "Limite de contexte des onglets ouverts", "description": "Nombre maximum d'onglets VSCode ouverts à inclure dans le contexte. Des valeurs plus élevées fournissent plus de contexte mais augmentent l'utilisation de token."}, "workspaceFiles": {"label": "Limite de contexte des fichiers de l'espace de travail", "description": "Nombre maximum de fichiers à inclure dans les détails du répertoire de travail actuel. Des valeurs plus élevées fournissent plus de contexte mais augmentent l'utilisation de token."}, "rooignore": {"label": "Afficher les fichiers .zhanluignore dans les listes et recherches", "description": "Lorsque cette option est activée, les fichiers correspondant aux modèles dans .zhanluignore seront affichés dans les listes avec un symbole de cadenas. Lorsqu'elle est désactivée, ces fichiers seront complètement masqués des listes de fichiers et des recherches."}, "maxReadFile": {"label": "Seuil d'auto-troncature de lecture de fichier", "description": "<PERSON><PERSON><PERSON> lit ce nombre de lignes lorsque le modèle omet les valeurs de début/fin. Si ce nombre est inférieur au total du fichier, Zhanlu génère un index des numéros de ligne des définitions de code. Cas spéciaux : -1 indique à Zhanlu de lire le fichier entier (sans indexation), et 0 indique de ne lire aucune ligne et de fournir uniquement les index de ligne pour un contexte minimal. Des valeurs plus basses minimisent l'utilisation initiale du contexte, permettant des lectures ultérieures de plages de lignes précises. Les requêtes avec début/fin explicites ne sont pas limitées par ce paramètre.", "lines": "lignes", "always_full_read": "Toujours lire le fichier entier"}, "maxConcurrentFileReads": {"label": "Limite de lectures simultanées", "description": "Nombre maximum de fichiers que l'outil 'read_file' peut traiter simultanément. Des valeurs plus élevées peuvent accélérer la lecture de plusieurs petits fichiers mais augmentent l'utilisation de la mémoire."}, "diagnostics": {"includeMessages": {"label": "Inclure automatiquement les diagnostics dans le contexte", "description": "Lorsque cette option est activée, les messages de diagnostic (erreurs) des fichiers modifiés seront automatiquement inclus dans le contexte. Vous pouvez toujours inclure manuellement tous les diagnostics de l'espace de travail en utilisant @problems."}, "maxMessages": {"label": "Nombre maximum de messages de diagnostic", "description": "Nombre maximum de messages de diagnostic à inclure par fichier. Cette limite s'applique à la fois à l'inclusion automatique (lorsque la case est cochée) et aux mentions manuelles @problems. Des valeurs plus élevées fournissent plus de contexte mais augmentent l'utilisation des jetons.", "resetTooltip": "Réinitialiser à la valeur par défaut (50)", "unlimitedLabel": "Illimité"}, "delayAfterWrite": {"label": "<PERSON><PERSON><PERSON> après les écritures pour permettre aux diagnostics de détecter les problèmes potentiels", "description": "Temps d'attente après les écritures de fichiers avant de continuer, permettant aux outils de diagnostic de traiter les modifications et de détecter les problèmes."}}, "condensingThreshold": {"label": "Seuil de condensation du contexte", "selectProfile": "Configurer le seuil pour le profil", "defaultProfile": "Par défaut global (tous les profils)", "defaultDescription": "Lors<PERSON> le contexte atteint ce pourcentage, il sera automatiquement condensé pour tous les profils sauf s'ils ont des paramètres personnalisés", "profileDescription": "Seuil personnalisé pour ce profil uniquement (remplace le défaut global)", "inheritDescription": "Ce profil hérite du seuil par défaut global ({{threshold}}%)", "usesGlobal": "(utilise global {{threshold}}%)"}}, "terminal": {"basic": {"label": "Paramètres du terminal : Base", "description": "Paramètres de base du terminal"}, "advanced": {"label": "Paramètres du terminal : Avancé", "description": "Les options suivantes peuvent nécessiter un redémarrage du terminal pour appliquer le paramètre."}, "outputLineLimit": {"label": "Limite de sortie du terminal", "description": "Nombre maximum de lignes à inclure dans la sortie du terminal lors de l'exécution de commandes. Lorsque ce nombre est dépassé, les lignes seront supprimées du milieu, économisant des token. <0>En savoir plus</0>"}, "outputCharacterLimit": {"label": "Limite de caractères du terminal", "description": "Nombre maximum de caractères à inclure dans la sortie du terminal lors de l'exécution de commandes. Cette limite prévaut sur la limite de lignes pour éviter les problèmes de mémoire avec des lignes extrêmement longues. Lorsque cette limite est dépassée, la sortie sera tronquée. <0>En savoir plus</0>"}, "shellIntegrationTimeout": {"label": "Délai d'intégration du shell du terminal", "description": "Temps maximum d'attente pour l'initialisation de l'intégration du shell avant d'exécuter des commandes. Pour les utilisateurs avec des temps de démarrage de shell longs, cette valeur peut nécessiter d'être augmentée si vous voyez des erreurs \"Shell Integration Unavailable\" dans le terminal. <0>En savoir plus</0>"}, "shellIntegrationDisabled": {"label": "Désactiver l'intégration du shell du terminal", "description": "Active ceci si les commandes du terminal ne fonctionnent pas correctement ou si tu vois des erreurs 'Shell Integration Unavailable'. Cela utilise une méthode plus simple pour exécuter les commandes, en contournant certaines fonctionnalités avancées du terminal. <0>En savoir plus</0>"}, "commandDelay": {"label": "<PERSON><PERSON><PERSON> de commande du terminal", "description": "Délai en millisecondes à ajouter après l'exécution de la commande. Le paramètre par défaut de 0 désactive complètement le délai. <PERSON><PERSON> peut aider à garantir que la sortie de la commande est entièrement capturée dans les terminaux avec des problèmes de synchronisation. Dans la plupart des terminaux, cela est implémenté en définissant `PROMPT_COMMAND='sleep N'` et Powershell ajoute `start-sleep` à la fin de chaque commande. À l'origine, c'était une solution pour le bug VSCode#237208 et peut ne pas être nécessaire. <0>En savoir plus</0>"}, "compressProgressBar": {"label": "Compresser la sortie des barres de progression", "description": "Lorsque activé, traite la sortie du terminal avec des retours chariot (\\r) pour simuler l'affichage d'un terminal réel. Cela supprime les états intermédiaires des barres de progression, ne conservant que l'état final, ce qui économise de l'espace de contexte pour des informations plus pertinentes. <0>En savoir plus</0>"}, "powershellCounter": {"label": "Activer le contournement du compteur PowerShell", "description": "Lors<PERSON><PERSON>'activ<PERSON>, ajoute un compteur aux commandes PowerShell pour assurer une exécution correcte des commandes. Cela aide avec les terminaux PowerShell qui peuvent avoir des problèmes de capture de sortie. <0>En savoir plus</0>"}, "zshClearEolMark": {"label": "Effacer la marque de fin de ligne ZSH", "description": "Lorsqu'activé, efface la marque de fin de ligne ZSH en définissant PROMPT_EOL_MARK=''. Cela évite les problèmes d'interprétation de la sortie des commandes lorsqu'elle se termine par des caractères spéciaux comme '%'. <0>En savoir plus</0>"}, "zshOhMy": {"label": "Activer l'intégration Oh My Zsh", "description": "Lorsqu'activé, définit ITERM_SHELL_INTEGRATION_INSTALLED=Yes pour activer les fonctionnalités d'intégration du shell Oh My Zsh. L'application de ce paramètre peut nécessiter le redémarrage de l'IDE. <0>En savoir plus</0>"}, "zshP10k": {"label": "Activer l'intégration Powerlevel10k", "description": "<PERSON><PERSON><PERSON><PERSON>'activé, définit POWERLEVEL9K_TERM_SHELL_INTEGRATION=true pour activer les fonctionnalités d'intégration du shell Powerlevel10k. <0>En savoir plus</0>"}, "zdotdir": {"label": "Activer la gestion ZDOTDIR", "description": "Lorsque activé, crée un répertoire temporaire pour ZDOTDIR afin de gérer correctement l'intégration du shell zsh. Cela garantit le bon fonctionnement de l'intégration du shell VSCode avec zsh tout en préservant votre configuration zsh. <0>En savoir plus</0>"}, "inheritEnv": {"label": "Hériter des variables d'environnement", "description": "<PERSON><PERSON><PERSON><PERSON>'activ<PERSON>, le terminal hérite des variables d'environnement du processus parent VSCode, comme les paramètres d'intégration du shell définis dans le profil utilisateur. Cela bascule directement le paramètre global VSCode `terminal.integrated.inheritEnv`. <0>En savoir plus</0>"}}, "advancedSettings": {"title": "Paramètres avancés"}, "advanced": {"diff": {"label": "Activer l'édition via des diffs", "description": "Lorsque cette option est activée, <PERSON>han<PERSON> pourra éditer des fichiers plus rapidement et rejettera automatiquement les écritures de fichiers complets tronqués. Fonctionne mieux avec le dernier modèle <PERSON>.", "strategy": {"label": "Stratégie de diff", "options": {"standard": "Standard (Bloc unique)", "multiBlock": "Expérimental : Diff multi-blocs", "unified": "Expérimental : Diff unifié"}, "descriptions": {"standard": "La stratégie de diff standard applique les modifications à un seul bloc de code à la fois.", "unified": "La stratégie de diff unifié prend plusieurs approches pour appliquer les diffs et choisit la meilleure approche.", "multiBlock": "La stratégie de diff multi-blocs permet de mettre à jour plusieurs blocs de code dans un fichier en une seule requête."}}, "matchPrecision": {"label": "Précision de correspondance", "description": "Ce curseur contrôle la précision avec laquelle les sections de code doivent correspondre lors de l'application des diffs. Des valeurs plus basses permettent des correspondances plus flexibles mais augmentent le risque de remplacements incorrects. Utilisez des valeurs inférieures à 100 % avec une extrême prudence."}}, "todoList": {"label": "Activer l'outil de liste de tâches", "description": "<PERSON><PERSON><PERSON><PERSON>'activ<PERSON>, <PERSON><PERSON><PERSON> peut créer et gérer des listes de tâches pour suivre la progression. <PERSON><PERSON> aide à organiser les tâches complexes en étapes gérables."}}, "completion": {"description": "Configurez les paramètres de complétion de code pour améliorer votre expérience de développement.", "configureButton": "Configurer"}, "experimental": {"DIFF_STRATEGY_UNIFIED": {"name": "Utiliser la stratégie diff unifiée expérimentale", "description": "Activer la stratégie diff unifiée expérimentale. Cette stratégie pourrait réduire le nombre de tentatives causées par des erreurs de modèle, mais peut provoquer des comportements inattendus ou des modifications incorrectes. Activez-la uniquement si vous comprenez les risques et êtes prêt à examiner attentivement tous les changements."}, "SEARCH_AND_REPLACE": {"name": "Utiliser l'outil de recherche et remplacement expérimental", "description": "Activer l'outil de recherche et remplacement expérimental, permettant à Zhanlu de remplacer plusieurs occurrences d'un terme de recherche en une seule requête."}, "INSERT_BLOCK": {"name": "Utiliser l'outil d'insertion de contenu expérimental", "description": "Activer l'outil d'insertion de contenu expérimental, permettant à Zhanlu d'insérer du contenu à des numéros de ligne spécifiques sans avoir besoin de créer un diff."}, "POWER_STEERING": {"name": "Utiliser le mode \"direction assistée\" expérimental", "description": "Lorsqu'il est activé, <PERSON><PERSON><PERSON> rappellera plus fréquemment au modèle les détails de sa définition de mode actuelle. Cela conduira à une adhérence plus forte aux définitions de rôles et aux instructions personnalisées, mais utilisera plus de tokens par message."}, "MULTI_SEARCH_AND_REPLACE": {"name": "Utiliser l'outil diff multi-blocs expérimental", "description": "Lorsqu'il est activé, <PERSON><PERSON><PERSON> utilisera l'outil diff multi-blocs. <PERSON>la tentera de mettre à jour plusieurs blocs de code dans le fichier en une seule requête."}, "CONCURRENT_FILE_READS": {"name": "Activer la lecture simultanée de fi<PERSON>", "description": "Lors<PERSON><PERSON>'activ<PERSON>, zhanlu peut lire plusieurs fichiers dans une seule requête. Lorsque désactivé, zhanlu doit lire les fichiers un par un. La désactivation peut aider lors du travail avec des modèles moins performants ou lorsque tu souhaites plus de contrôle sur l'accès aux fichiers."}, "MARKETPLACE": {"name": "<PERSON>r le <PERSON>place", "description": "Lorsque cette option est activée, tu peux installer des MCP et des modes personnalisés depuis le Marketplace."}, "MULTI_FILE_APPLY_DIFF": {"name": "Activer les éditions de fichiers concurrentes", "description": "Lorsque cette option est activée, zhanlu peut éditer plusieurs fichiers en une seule requête. Lorsqu'elle est désactivée, zhanlu doit éditer les fichiers un par un. Désactiver cette option peut aider lorsque tu travailles avec des modèles moins capables ou lorsque tu veux plus de contrôle sur les modifications de fichiers."}}, "promptCaching": {"label": "Désactiver la mise en cache des prompts", "description": "Lorsque cette option est cochée, <PERSON>han<PERSON> n'utilisera pas la mise en cache des prompts pour ce modèle."}, "temperature": {"useCustom": "Utiliser une température personnalisée", "description": "Contrôle l'aléatoire dans les réponses du modèle.", "rangeDescription": "Des valeurs plus élevées rendent la sortie plus aléatoire, des valeurs plus basses la rendent plus déterministe."}, "modelInfo": {"supportsImages": "Prend en charge les images", "noImages": "Ne prend pas en charge les images", "supportsComputerUse": "Prend en charge l'utilisation de l'ordinateur", "noComputerUse": "Ne prend pas en charge l'utilisation de l'ordinateur", "supportsPromptCache": "Prend en charge la mise en cache des prompts", "noPromptCache": "Ne prend pas en charge la mise en cache des prompts", "maxOutput": "Sortie maximale", "inputPrice": "Prix d'entrée", "outputPrice": "Prix de sortie", "cacheReadsPrice": "Prix des lectures de cache", "cacheWritesPrice": "Prix des écritures de cache", "enableStreaming": "Activer le <PERSON>", "enableR1Format": "Activer les paramètres du modèle R1", "enableR1FormatTips": "Doit être activé lors de l'utilisation de modèles R1 tels que QWQ, pour éviter l'erreur 400", "useAzure": "Utiliser Azure", "azureApiVersion": "Définir la version de l'API Azure", "gemini": {"freeRequests": "* Gratuit jusqu'à {{count}} requêtes par minute. <PERSON><PERSON> cela, la facturation dépend de la taille du prompt.", "pricingDetails": "Pour plus d'informations, voir les détails de tarification.", "billingEstimate": "* La facturation est une estimation - le coût exact dépend de la taille du prompt."}}, "modelPicker": {"automaticFetch": "L'extension récupère automatiquement la liste la plus récente des modèles disponibles sur <serviceLink>{{serviceName}}</serviceLink>. Si vous ne savez pas quel modèle choisir, <PERSON><PERSON><PERSON> fonctionne mieux avec <defaultModelLink>{{defaultModelId}}</defaultModelLink>. Vous pouvez également rechercher \"free\" pour les options gratuites actuellement disponibles.", "label": "<PERSON><PERSON><PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON>", "noMatchFound": "Aucune correspondance trouvée", "useCustomModel": "Utiliser personnalisé : {{modelId}}"}, "footer": {"feedback": "Si vous avez des questions ou des commentaires, n'hésitez pas à <qqDocsLink>ouvrir un ticket</qqDocsLink>.", "version": "<PERSON><PERSON><PERSON> v{{version}}", "telemetry": {"label": "Autoriser les rapports anonymes d'erreurs et d'utilisation", "description": "Aidez à améliorer <PERSON> en envoyant des données d'utilisation anonymes et des rapports d'erreurs. Aucun code, prompt ou information personnelle n'est jamais envoyé. Consultez notre politique de confidentialité pour plus de détails."}, "settings": {"import": "Importer", "export": "Exporter", "reset": "Réinitialiser"}}, "thinkingBudget": {"maxTokens": "Tokens maximum", "maxThinkingTokens": "Tokens de réflexion maximum"}, "validation": {"apiKey": "V<PERSON> devez fournir une clé API valide.", "awsRegion": "Vous devez choisir une région pour utiliser Amazon Bedrock.", "googleCloud": "V<PERSON> devez fournir un ID de projet et une région Google Cloud valides.", "modelId": "V<PERSON> devez fournir un ID de modèle valide.", "modelSelector": "V<PERSON> devez fournir un sélecteur de modèle valide.", "openAi": "Vous devez fournir une URL de base, une clé API et un ID de modèle valides.", "arn": {"invalidFormat": "Format ARN invalide. Veuillez vérifier les exigences de format.", "regionMismatch": "Attention : La région dans votre ARN ({{arnRegion}}) ne correspond pas à votre région sélectionnée ({{region}}). Cela peut causer des problèmes d'accès. Le fournisseur utilisera la région de l'ARN."}, "modelAvailability": "L'ID de modèle ({{modelId}}) que vous avez fourni n'est pas disponible. Veuillez choisir un modèle différent.", "providerNotAllowed": "Le fournisseur '{{provider}}' n'est pas autorisé par votre organisation", "modelNotAllowed": "Le modèle '{{model}}' n'est pas autorisé pour le fournisseur '{{provider}}' par votre organisation", "profileInvalid": "Ce profil contient un fournisseur ou un modèle qui n'est pas autorisé par votre organisation"}, "placeholders": {"apiKey": "Saisissez la clé API...", "profileName": "Saisis<PERSON>z le nom du profil", "accessKey": "Saisissez la clé d'accès...", "secretKey": "Saisissez la clé secrète...", "sessionToken": "Saisis<PERSON>z le jeton de session...", "credentialsJson": "Saisissez le JSON des identifiants...", "keyFilePath": "Sai<PERSON><PERSON>z le chemin du fichier de clé...", "projectId": "Saisissez l'ID du projet...", "customArn": "Saisissez l'ARN (ex. arn:aws:bedrock:us-east-1:123456789012:foundation-model/my-model)", "baseUrl": "Saisissez l'URL de base...", "modelId": {"lmStudio": "ex. meta-llama-3.1-8b-instruct", "lmStudioDraft": "ex. lmstudio-community/llama-3.2-1b-instruct", "ollama": "ex. llama3.1"}, "numbers": {"maxTokens": "ex. 4096", "contextWindow": "ex. 128000", "inputPrice": "ex. 0.0001", "outputPrice": "ex. 0.0002", "cacheWritePrice": "ex. 0.00005"}}, "defaults": {"ollamaUrl": "Par défaut : http://localhost:11434", "lmStudioUrl": "Par défaut : http://localhost:1234", "geminiUrl": "Par défaut : https://generativelanguage.googleapis.com"}, "labels": {"customArn": "ARN personnalisé", "useCustomArn": "Utiliser un ARN personnalisé..."}, "includeMaxOutputTokens": "Inclure les tokens de sortie maximum", "includeMaxOutputTokensDescription": "Envoyer le paramètre de tokens de sortie maximum dans les requêtes API. Certains fournisseurs peuvent ne pas supporter cela.", "limitMaxTokensDescription": "Limiter le nombre maximum de tokens dans la réponse", "maxOutputTokensLabel": "Tokens de sortie maximum", "maxTokensGenerateDescription": "Tokens maximum à générer dans la réponse"}